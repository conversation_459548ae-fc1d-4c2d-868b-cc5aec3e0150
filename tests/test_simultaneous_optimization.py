#!/usr/bin/env python3
"""
Test Suite for Simultaneous Optimization System
Tests the hierarchical optimizer, constraint system, and integration with existing aligner.
"""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from data_handler import YemenDataHandler
from target_processor import IMFTargetProcessor
from identity_preserving_aligner import IdentityPreservingAligner
from hierarchical_optimizer import HierarchicalOptimizer, OptimizationResult
from sparse_constraints import SparseConstraintSystem
from block_solver import BlockCoordinateDescent
from feasibility_manager import FeasibilityManager
from solver_enhancements import SolverEnhancements


class TestHierarchicalOptimizer:
    """Test the hierarchical optimizer"""
    
    @pytest.fixture
    def data_handler(self):
        """Create data handler for testing"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        return handler
    
    @pytest.fixture
    def target_processor(self):
        """Create target processor for testing"""
        return IMFTargetProcessor(
            'config/imf_targets.yaml',
            'config/adjustment_rules.yaml'
        )
    
    @pytest.fixture
    def optimizer(self, data_handler, target_processor):
        """Create hierarchical optimizer for testing"""
        return HierarchicalOptimizer(data_handler, target_processor, tolerance=5.0)
    
    def test_initialization(self, optimizer):
        """Test optimizer initialization"""
        assert optimizer is not None
        assert optimizer.tolerance == 5.0
        assert len(optimizer.deflator_hierarchy) == 3  # core, sectoral, component
        assert len(optimizer.variable_mapping) > 10  # Should have many deflator mappings
    
    def test_deflator_hierarchy(self, optimizer):
        """Test deflator hierarchy structure"""
        hierarchy = optimizer.deflator_hierarchy
        
        # Check structure
        assert 'core' in hierarchy
        assert 'sectoral' in hierarchy
        assert 'component' in hierarchy
        
        # Check core deflators
        assert 'gdp_aggregate' in hierarchy['core']
        assert 'gdp_deflator' in hierarchy['core']['gdp_aggregate']
    
    def test_variable_mapping(self, optimizer):
        """Test variable mapping structure"""
        mapping = optimizer.variable_mapping
        
        # Check key deflators exist
        assert 'gdp_deflator' in mapping
        assert 'c_private_deflator' in mapping
        assert 'imports_deflator' in mapping
        
        # Check mapping structure
        gdp_mapping = mapping['gdp_deflator']
        assert 'real' in gdp_mapping
        assert 'nominal' in gdp_mapping
        assert gdp_mapping['real'] == 'YEMNYGDPMKTPKN'
        assert gdp_mapping['nominal'] == 'YEMNYGDPMKTPCN'
    
    def test_get_current_deflators(self, optimizer):
        """Test getting current deflator values"""
        years = [2022, 2023]
        deflators = optimizer._get_current_deflators(years)
        
        assert isinstance(deflators, dict)
        assert 'gdp_deflator' in deflators
        assert 2022 in deflators['gdp_deflator']
        assert 2023 in deflators['gdp_deflator']
        
        # Check deflator values are reasonable
        gdp_deflator_2022 = deflators['gdp_deflator'][2022]
        assert 50 < gdp_deflator_2022 < 500  # Reasonable range for crisis economy
    
    def test_optimization_setup(self, optimizer):
        """Test optimization problem setup"""
        years = [2022, 2023]
        bounds, constraints = optimizer._setup_optimization_problem(years)
        
        # Check bounds
        assert isinstance(bounds, list)
        assert len(bounds) > 0
        
        # Each bound should be a tuple (min, max)
        for bound in bounds:
            assert isinstance(bound, tuple)
            assert len(bound) == 2
            assert bound[0] < bound[1]  # min < max
    
    def test_basic_optimization(self, optimizer):
        """Test basic optimization functionality"""
        years = [2022]  # Test with single year for speed
        
        # This should not crash and should return a result
        result = optimizer.optimize(years)
        
        assert isinstance(result, OptimizationResult)
        assert isinstance(result.success, bool)
        assert isinstance(result.deflator_adjustments, dict)
        assert isinstance(result.error_messages, list)


class TestSparseConstraintSystem:
    """Test the sparse constraint system"""
    
    @pytest.fixture
    def data_handler(self):
        """Create data handler for testing"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        return handler
    
    @pytest.fixture
    def constraint_system(self, data_handler):
        """Create constraint system for testing"""
        return SparseConstraintSystem(data_handler, tolerance=5.0)
    
    def test_initialization(self, constraint_system):
        """Test constraint system initialization"""
        assert constraint_system is not None
        assert constraint_system.tolerance == 5.0
        assert len(constraint_system.constraints) > 0
    
    def test_constraint_definitions(self, constraint_system):
        """Test constraint definitions"""
        constraints = constraint_system.constraints
        
        # Check key constraints exist
        assert 'gdp_expenditure_nominal' in constraints
        assert 'gdp_expenditure_real' in constraints
        assert 'gdp_production_nominal' in constraints
        assert 'investment_decomposition' in constraints
        
        # Check constraint structure
        gdp_constraint = constraints['gdp_expenditure_nominal']
        assert gdp_constraint.name == 'GDP Expenditure (Nominal)'
        assert gdp_constraint.constraint_type == 'equality'
        assert len(gdp_constraint.variables) > 0
        assert len(gdp_constraint.coefficients) == len(gdp_constraint.variables)
    
    def test_constraint_evaluation(self, constraint_system):
        """Test constraint evaluation"""
        years = [2022]
        
        # Create dummy deflator values
        deflators = {
            'gdp_deflator': {2022: 100.0},
            'c_private_deflator': {2022: 100.0},
            'imports_deflator': {2022: 100.0}
        }
        
        # This should not crash
        violations = constraint_system.evaluate_constraints(deflators, years)
        
        assert isinstance(violations, dict)
        assert len(violations) > 0
        
        # Check structure
        for constraint_name, year_violations in violations.items():
            assert isinstance(year_violations, dict)
            for year, violation in year_violations.items():
                assert isinstance(violation, (int, float))


class TestBlockSolver:
    """Test the block coordinate descent solver"""
    
    @pytest.fixture
    def data_handler(self):
        """Create data handler for testing"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        return handler
    
    @pytest.fixture
    def target_processor(self):
        """Create target processor for testing"""
        return IMFTargetProcessor(
            'config/imf_targets.yaml',
            'config/adjustment_rules.yaml'
        )
    
    @pytest.fixture
    def block_solver(self, data_handler, target_processor):
        """Create block solver for testing"""
        return BlockCoordinateDescent(data_handler, target_processor)
    
    def test_initialization(self, block_solver):
        """Test block solver initialization"""
        assert block_solver is not None
        assert len(block_solver.blocks) > 0
    
    def test_block_structure(self, block_solver):
        """Test block structure definition"""
        blocks = block_solver.blocks
        
        # Check key blocks exist
        assert 'gdp_block' in blocks
        assert 'trade_block' in blocks
        assert 'fiscal_block' in blocks
        
        # Check block structure
        gdp_block = blocks['gdp_block']
        assert 'deflators' in gdp_block
        assert 'primary_targets' in gdp_block
        assert 'constraints' in gdp_block
        assert len(gdp_block['deflators']) > 0


class TestFeasibilityManager:
    """Test the feasibility manager"""
    
    @pytest.fixture
    def feasibility_manager(self):
        """Create feasibility manager for testing"""
        return FeasibilityManager(crisis_tolerance=5.0)
    
    def test_initialization(self, feasibility_manager):
        """Test feasibility manager initialization"""
        assert feasibility_manager is not None
        assert feasibility_manager.crisis_tolerance == 5.0
        assert len(feasibility_manager.crisis_constraints) > 0
        assert len(feasibility_manager.critical_constraints) > 0
    
    def test_crisis_constraints(self, feasibility_manager):
        """Test crisis constraint definitions"""
        crisis_constraints = feasibility_manager.crisis_constraints
        
        # Check key crisis constraints
        assert 'bop_identity' in crisis_constraints
        assert 'savings_investment' in crisis_constraints
        
        # Check constraint structure
        bop_constraint = crisis_constraints['bop_identity']
        assert 'description' in bop_constraint
        assert 'max_relaxation' in bop_constraint
        assert 'penalty_weight' in bop_constraint
        assert 'justification' in bop_constraint
    
    def test_feasibility_analysis(self, feasibility_manager):
        """Test feasibility analysis"""
        # Create dummy constraint violations
        violations = {
            'gdp_expenditure_nominal': {2022: 2.0, 2023: 1.5},  # Small violations
            'bop_identity': {2022: 8.0, 2023: 6.0},  # Large violations (crisis)
            'savings_investment': {2022: 12.0}  # Very large violation
        }
        
        years = [2022, 2023]
        report = feasibility_manager.analyze_feasibility(violations, years)
        
        assert report is not None
        assert isinstance(report.is_feasible, bool)
        assert isinstance(report.infeasible_constraints, list)
        assert isinstance(report.slack_variables, dict)
        assert isinstance(report.recommendations, list)


class TestIntegration:
    """Test integration with existing aligner"""
    
    @pytest.fixture
    def aligner(self):
        """Create aligner with simultaneous optimization"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        
        processor = IMFTargetProcessor(
            'config/imf_targets.yaml',
            'config/adjustment_rules.yaml'
        )
        
        return IdentityPreservingAligner(
            data_handler=handler,
            target_processor=processor,
            tolerance=5.0
        )
    
    def test_aligner_initialization(self, aligner):
        """Test that aligner initializes with simultaneous optimization"""
        assert aligner is not None
        assert hasattr(aligner, 'simultaneous_optimizer')
        assert hasattr(aligner, 'use_simultaneous')
        
        # Should have simultaneous optimizer if modules are available
        if aligner.simultaneous_optimizer is not None:
            assert aligner.use_simultaneous is True
    
    def test_alignment_with_simultaneous(self, aligner):
        """Test alignment using simultaneous optimization"""
        years = [2022]  # Single year for speed
        
        # Force simultaneous optimization if available
        if aligner.simultaneous_optimizer is not None:
            aligner.use_simultaneous = True
            
            result = aligner.align_to_targets(years)
            
            assert result is not None
            assert hasattr(result, 'success')
            assert isinstance(result.success, bool)
    
    def test_fallback_to_sequential(self, aligner):
        """Test fallback to sequential optimization"""
        years = [2022]
        
        # Force sequential optimization
        aligner.use_simultaneous = False
        
        result = aligner.align_to_targets(years)
        
        assert result is not None
        assert hasattr(result, 'success')
        assert isinstance(result.success, bool)


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
