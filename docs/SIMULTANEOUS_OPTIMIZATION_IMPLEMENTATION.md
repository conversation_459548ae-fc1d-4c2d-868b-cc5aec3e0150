# Simultaneous Optimization Implementation Guide

## Project Context
You are implementing an advanced simultaneous optimization system for the Yemen IMF Alignment Tool. The current system adjusts deflators sequentially, which can break previously satisfied constraints. Your task is to implement a sophisticated simultaneous optimization approach that maintains all 13 economic identities while achieving 18 IMF targets.

## Current System Overview
- **Location**: `/macroeconomic-framework/`
- **Main Script**: `align_to_imf.py`
- **Core Module**: `src/identity_preserving_aligner.py`
- **Current Approach**: Sequential deflator adjustments
- **Problem**: Sequential adjustments can break previously satisfied identities

## Mathematical Formulation

### Objective Function
```
minimize: Σ w_i * (target_i - achieved_i)²
```
Where:
- `w_i` = weights for each IMF target (prioritize GDP, fiscal balance)
- `target_i` = IMF target values from `config/imf_targets.yaml`
- `achieved_i` = calculated values after deflator adjustments

### Decision Variables
- All deflators for years 2022-2025 (~40-60 variables)
- Organized in 3-layer hierarchy:
  - Layer 1: Core deflators (GDP, CPI)
  - Layer 2: Sectoral deflators
  - Layer 3: Component deflators

### Constraints
1. **Economic Identities** (13 equality constraints):
   - GDP expenditure approach (nominal & real)
   - GDP production approach (nominal & real)
   - Fiscal identities (balance = revenue - expenditure)
   - Balance of payments identity
   - Investment decomposition
   - Trade consistency
   - etc.

2. **Fundamental Relationships**:
   - Nominal = Real × (Deflator/100) for all variables
   - Real values remain constant (never modify)

3. **Bounds**:
   - 20% < deflator < 200% (reasonable bounds)

4. **Crisis Economy Gaps**:
   - L1-penalized slack variables for acceptable identity violations
   - Example: Savings-Investment gap in crisis economies

## Implementation Tasks

### 1. Create `src/hierarchical_optimizer.py`
```python
class HierarchicalOptimizer:
    """
    Implements Augmented Lagrangian with hierarchical deflator structure
    """
    def __init__(self, data_handler, target_processor, tolerance=0.05):
        self.deflator_hierarchy = self._build_hierarchy()
        self.penalty_param = 10.0  # ρ for augmented Lagrangian
        self.lagrange_multipliers = {}
        
    def _build_hierarchy(self):
        """Define 3-layer deflator hierarchy"""
        return {
            'core': ['gdp_deflator', 'cpi'],
            'sectoral': ['govt_deflator', 'trade_deflator', ...],
            'component': ['imports_deflator', 'exports_deflator', ...]
        }
    
    def optimize(self, years):
        """Main optimization using block coordinate descent"""
        # Implementation here
```

### 2. Create `src/block_solver.py`
```python
class BlockCoordinateDescent:
    """
    Solves optimization problem using block structure
    """
    def solve_gdp_block(self, fixed_vars, lagrange_mult):
        """Optimize GDP-related deflators"""
        pass
        
    def solve_fiscal_block(self, fixed_vars, lagrange_mult):
        """Optimize government deflators"""
        pass
        
    def solve_trade_block(self, fixed_vars, lagrange_mult):
        """Optimize trade deflators"""
        pass
        
    def update_multipliers(self, constraint_violations):
        """Update Lagrange multipliers"""
        pass
```

### 3. Create `src/sparse_constraints.py`
```python
class SparseConstraintSystem:
    """
    Efficient constraint evaluation using sparse matrices
    """
    def __init__(self, data_handler):
        self.sparsity_pattern = self._compute_sparsity()
        self.constraint_cache = {}
        
    def evaluate_constraints(self, deflators):
        """Evaluate all identity constraints efficiently"""
        pass
        
    def compute_jacobian(self, deflators):
        """Compute sparse Jacobian matrix"""
        pass
```

### 4. Create `src/solver_enhancements.py`
```python
class SolverEnhancements:
    """
    Anderson acceleration and trust region methods
    """
    def anderson_accelerate(self, x_history, f_history, m=5):
        """Anderson acceleration for fixed-point iterations"""
        pass
        
    def trust_region_step(self, x, grad, hess, delta):
        """Compute step within trust region"""
        pass
```

### 5. Create `src/feasibility_manager.py`
```python
class FeasibilityManager:
    """
    Handle over-constrained systems intelligently
    """
    def __init__(self, crisis_tolerance=0.05):
        self.slack_variables = {}
        self.relaxation_penalties = {}
        
    def add_slack_variables(self, constraints):
        """Add L1-penalized slack for crisis economy gaps"""
        pass
        
    def detect_infeasibility(self, constraint_residuals):
        """Identify which constraints need relaxation"""
        pass
```

### 6. Integration with Existing System

Modify `src/identity_preserving_aligner.py`:
```python
def align_to_targets(self, years):
    """Enhanced alignment using simultaneous optimization"""
    if self.use_simultaneous:
        optimizer = HierarchicalOptimizer(self.data, self.targets)
        result = optimizer.optimize(years)
    else:
        # Fall back to sequential method
        result = self._sequential_align(years)
    return result
```

## Key Implementation Details

### Augmented Lagrangian Formulation
```
L(x, λ, ξ) = f(x) + λᵀh(x) + (ρ/2)||h(x)||² + μ||ξ||₁
```
Where:
- `f(x)` = sum of squared deviations from targets
- `h(x)` = identity constraint violations
- `ξ` = slack variables for crisis gaps
- `λ` = Lagrange multipliers
- `ρ` = penalty parameter (start at 10, adapt)
- `μ` = L1 penalty weight for slack

### Block Structure Exploitation
1. **GDP Block**: Variables affecting GDP identities
2. **Fiscal Block**: Government revenue/expenditure deflators
3. **Trade Block**: Import/export deflators
4. **Cross-block constraints**: Handle via Lagrange multipliers

### Convergence Criteria
- Constraint violation < tolerance (0.05% for crisis economy)
- Objective function change < 1e-6
- Maximum iterations = 1000
- Trust region radius > 1e-8

### Performance Optimizations
- Pre-compute and cache Jacobian sparsity pattern
- Use scipy.sparse for matrix operations
- Warm-start from current deflator values
- Parallelize block solves when possible

## Testing Requirements

1. **Unit Tests** (`tests/test_simultaneous_optimization.py`):
   - Test each solver component
   - Verify constraint satisfaction
   - Check convergence behavior

2. **Integration Tests**:
   - Compare results with sequential approach
   - Verify all identities maintained
   - Check target achievement

3. **Performance Tests**:
   - Measure convergence speed
   - Profile memory usage
   - Test on full Yemen dataset

## Expected Outcomes

1. **All 13 economic identities preserved** (within crisis tolerance)
2. **18 IMF targets achieved** (or optimal approximation)
3. **Faster convergence** than sequential approach
4. **Robust handling** of crisis economy constraints
5. **Clear diagnostics** showing which identities require gaps

## Additional Resources

- Current sequential implementation: `src/identity_preserving_aligner.py`
- Identity definitions: `src/identity_validator.py`
- IMF targets: `config/imf_targets.yaml`
- Yemen macro data: `data/yemen_macro_data.csv`

## Success Criteria

Your implementation is successful when:
1. The optimizer converges on Yemen data
2. Identity violations are < 5% (crisis tolerance)
3. IMF target achievement is ≥ current sequential method
4. Clear diagnostic output shows optimization progress
5. Fallback to sequential method works if optimization fails

## Questions to Consider

1. How to handle deflators that affect multiple identities?
2. What's the optimal block decomposition strategy?
3. How to adaptively adjust penalty parameters?
4. When to allow identity relaxation vs. strict enforcement?
5. How to provide meaningful progress feedback during optimization?

Good luck with the implementation! The key is exploiting the economic structure of the problem while maintaining mathematical rigor.