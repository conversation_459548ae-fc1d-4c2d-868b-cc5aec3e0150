# Simultaneous Optimization System - Critical Fixes Implementation Guide

## Project Context
You are fixing critical implementation gaps in a simultaneous optimization system for the Yemen IMF Alignment Tool. The existing implementation has the correct architecture but uses placeholder/simplified implementations for core mathematical components. Your task is to implement the proper mathematical algorithms.

## Current System Overview
- **Location**: `/macroeconomic-framework/`
- **Core Module**: `src/hierarchical_optimizer.py`
- **Supporting Modules**: `block_solver.py`, `sparse_constraints.py`, `solver_enhancements.py`, `feasibility_manager.py`
- **Problem**: The system falls back to basic scipy optimization instead of using the sophisticated Augmented Lagrangian method with block coordinate descent as designed

## Mathematical Foundation to Implement

### Augmented Lagrangian Formulation
The system should solve:
```
min L(x, λ, ξ) = f(x) + λᵀh(x) + (ρ/2)||h(x)||² + μ||ξ||₁
```
Where:
- `x` = deflator variables (decision variables)
- `f(x)` = weighted sum of squared deviations from IMF targets
- `h(x)` = constraint violations (13 economic identities)
- `λ` = Lagrange multipliers
- `ρ` = penalty parameter
- `ξ` = slack variables for crisis constraints
- `μ` = L1 penalty weight for slack

## Critical Fixes Required

### 1. Fix `hierarchical_optimizer.py` - Core Optimization

**Location**: `_run_augmented_lagrangian` method (line 334)

**Current Issue**: Falls back to basic scipy optimization without constraints

**Required Implementation**:
```python
def _run_augmented_lagrangian(self, initial_deflators, years, bounds, constraints):
    """Implement proper Augmented Lagrangian with block coordinate descent"""
    
    # Initialize
    x_current = self._deflators_to_vector(initial_deflators, years)
    slack_current = np.zeros(len(self.feasibility_manager.slack_variables))
    
    for outer_iter in range(self.max_iterations):
        # Step 1: Minimize augmented Lagrangian with current multipliers
        def augmented_objective(x_and_slack):
            x = x_and_slack[:len(x_current)]
            slack = x_and_slack[len(x_current):]
            
            # Apply deflators and calculate targets
            deflators = self._vector_to_deflators(x, years)
            self._apply_deflators_temporarily(deflators, years)
            
            # f(x): Target deviations
            target_dev = self._calculate_target_deviations(years)
            
            # h(x): Constraint violations
            violations = self.constraint_system.evaluate_constraints(deflators, years)
            
            # Augmented Lagrangian terms
            penalty_term = 0.0
            multiplier_term = 0.0
            
            for constraint_name, year_violations in violations.items():
                for year, violation in year_violations.items():
                    # λᵀh(x)
                    if constraint_name in self.lagrange_multipliers['identity_constraints']:
                        λ = self.lagrange_multipliers['identity_constraints'][constraint_name][year]
                        multiplier_term += λ * violation
                    
                    # (ρ/2)||h(x)||²
                    penalty_term += (self.penalty_param / 2) * violation**2
            
            # μ||ξ||₁ for slack variables
            slack_penalty = self.feasibility_manager.get_penalty_term(
                self._vector_to_slack_dict(slack, years)
            )
            
            return target_dev + multiplier_term + penalty_term + slack_penalty
        
        # Step 2: Use block coordinate descent
        if self.block_solver:
            result = self._solve_with_blocks(augmented_objective, x_current, slack_current, years)
        else:
            # Fallback to scipy
            x_and_slack = np.concatenate([x_current, slack_current])
            result = opt.minimize(
                augmented_objective,
                x_and_slack,
                method='L-BFGS-B',
                bounds=bounds + slack_bounds
            )
            x_current = result.x[:len(x_current)]
            slack_current = result.x[len(x_current):]
        
        # Step 3: Update Lagrange multipliers
        violations = self.constraint_system.evaluate_constraints(
            self._vector_to_deflators(x_current, years), years
        )
        self.lagrange_multipliers = self.block_solver.update_multipliers(
            violations, self.lagrange_multipliers, self.penalty_param
        )
        
        # Step 4: Check convergence
        max_violation = max(
            abs(v) for year_dict in violations.values() 
            for v in year_dict.values()
        )
        
        if max_violation < self.constraint_tolerance:
            logger.info(f"Converged after {outer_iter + 1} iterations")
            break
        
        # Step 5: Update penalty parameter if needed
        if outer_iter > 0 and max_violation > prev_max_violation * 0.9:
            self.penalty_param = min(self.penalty_param * self.penalty_increase, self.max_penalty)
            logger.info(f"Increased penalty parameter to {self.penalty_param}")
        
        prev_max_violation = max_violation
    
    # Return result
    optimal_deflators = self._vector_to_deflators(x_current, years)
    return OptimizationResult(
        success=True,
        deflator_adjustments=optimal_deflators,
        objective_value=augmented_objective(np.concatenate([x_current, slack_current])),
        constraint_violations=violations,
        iterations=outer_iter + 1
    )
```

### 2. Fix `block_solver.py` - Implement Actual Block Optimization

**Location**: Block solving methods (lines 233-368)

**Current Issue**: Uses simplified single-deflator updates instead of proper optimization

**Required Implementation**:
```python
def _solve_gdp_block(self, block_info, current_deflators, years, lagrange_multipliers):
    """Solve GDP block with proper optimization"""
    
    # Get deflators for this block
    block_deflators = block_info['deflators']
    block_indices = self._get_deflator_indices(block_deflators, years)
    
    # Create sub-problem for this block
    def block_objective(x_block):
        # Update only this block's deflators
        x_full = self._get_full_vector(current_deflators, years)
        x_full[block_indices] = x_block
        
        # Calculate augmented Lagrangian for this block
        deflators = self._vector_to_deflators(x_full, years)
        
        # Target deviations (only GDP-related)
        gdp_dev = self._calculate_gdp_deviation(deflators, years)
        
        # Constraint violations for GDP identities
        gdp_constraints = ['gdp_expenditure_nominal', 'gdp_expenditure_real']
        violation_sum = 0.0
        multiplier_sum = 0.0
        
        for constraint_name in gdp_constraints:
            if constraint_name in block_info['constraints']:
                violations = self._evaluate_single_constraint(constraint_name, deflators, years)
                for year, violation in violations.items():
                    # Augmented Lagrangian terms
                    λ = lagrange_multipliers['identity_constraints'][constraint_name][year]
                    multiplier_sum += λ * violation
                    violation_sum += (self.penalty_param / 2) * violation**2
        
        return gdp_dev + multiplier_sum + violation_sum
    
    # Get current block values
    x_block_init = current_deflators[block_indices]
    
    # Solve sub-problem
    result = opt.minimize(
        block_objective,
        x_block_init,
        method='L-BFGS-B',
        bounds=[(20.0, 200.0)] * len(x_block_init)
    )
    
    # Update deflator values
    updated_deflators = {}
    for i, deflator_name in enumerate(block_deflators):
        updated_deflators[deflator_name] = result.x[i]
    
    return BlockSolution(
        success=result.success,
        deflator_values=updated_deflators,
        objective_value=result.fun,
        constraint_violations=self._calculate_block_violations(updated_deflators, years),
        iterations=result.nit
    )
```

### 3. Fix `sparse_constraints.py` - Implement Constraint Evaluation

**Location**: `_apply_deflators_temporarily` method (line 317)

**Current Issue**: Empty implementation (just `pass`)

**Required Implementation**:
```python
def _apply_deflators_temporarily(self, deflators: Dict[str, Dict[int, float]], years: List[int]):
    """Apply deflators temporarily for constraint evaluation"""
    
    # Map deflator names to variable codes
    deflator_mapping = {
        'gdp_deflator': ('YEMNYGDPMKTPKN', 'YEMNYGDPMKTPCN'),
        'c_private_deflator': ('YEMNECONPRVTKN', 'YEMNECONPRVTCN'),
        'c_government_deflator': ('YEMNECONGOVTKN', 'YEMNECONGOVTCN'),
        'i_total_deflator': ('YEMNEGDIFTOTKN', 'YEMNEGDIFTOTCN'),
        'i_private_deflator': ('YEMNEGDIFPRVKN', 'YEMNEGDIFPRVCN'),
        'i_government_deflator': ('YEMNEGDIFGOVKN', 'YEMNEGDIFGOVCN'),
        'inventory_deflator': ('YEMNEGDISTKBKN', 'YEMNEGDISTKBCN'),
        'exports_deflator': ('YEMNEEXPGNFSKN', 'YEMNEEXPGNFSCN'),
        'imports_deflator': ('YEMNEIMPGNFSKN', 'YEMNEIMPGNFSCN'),
        'stat_disc_deflator': ('YEMNYGDPDISCKN', 'YEMNYGDPDISCCN'),
        'agriculture_deflator': ('YEMNVAGRTOTLKN', 'YEMNVAGRTOTLCN'),
        'industry_deflator': ('YEMNVINDTOTLKN', 'YEMNVINDTOTLCN'),
        'services_deflator': ('YEMNVSRVTOTLKN', 'YEMNVSRVTOTLCN'),
        'net_taxes_deflator': ('YEMNYTAXNINDKN', 'YEMNYTAXNINDCN'),
    }
    
    for deflator_name, year_values in deflators.items():
        if deflator_name in deflator_mapping:
            real_var, nominal_var = deflator_mapping[deflator_name]
            
            for year in years:
                if year in year_values:
                    # Get real value
                    real_value = self.data.get_variable(real_var, [year]).get(year, 0)
                    
                    if real_value != 0 and not pd.isna(real_value):
                        # Apply deflator: Nominal = Real × Deflator/100
                        new_deflator = year_values[year]
                        new_nominal = real_value * new_deflator / 100
                        
                        # Update nominal value
                        self.data.update_variable(nominal_var, year, new_nominal)
```

### 4. Fix `sparse_constraints.py` - Implement Proper Jacobian

**Location**: `compute_jacobian` method (line 250)

**Current Issue**: Simplified implementation with dummy coefficients

**Required Implementation**:
```python
def compute_jacobian(self, deflators: Dict[str, Dict[int, float]], years: List[int]) -> csr_matrix:
    """Compute sparse Jacobian matrix of constraints with respect to deflators"""
    
    # Setup
    num_constraints = len(self.constraints) * len(years)
    num_variables = sum(len(year_vals) for year_vals in deflators.values())
    
    # Use finite differences for Jacobian computation
    epsilon = 1e-6
    jacobian = lil_matrix((num_constraints, num_variables))
    
    # Current constraint values
    current_violations = self.evaluate_constraints(deflators, years)
    
    # Compute derivatives via finite differences
    var_idx = 0
    for deflator_name in deflators.keys():
        for year in years:
            if year in deflators[deflator_name]:
                # Perturb this deflator
                deflators_perturbed = deepcopy(deflators)
                deflators_perturbed[deflator_name][year] += epsilon
                
                # Evaluate constraints with perturbation
                perturbed_violations = self.evaluate_constraints(deflators_perturbed, years)
                
                # Compute derivatives
                constraint_idx = 0
                for constraint_name in self.constraints.keys():
                    for constraint_year in years:
                        if constraint_year in current_violations.get(constraint_name, {}):
                            # Finite difference derivative
                            current_val = current_violations[constraint_name][constraint_year]
                            perturbed_val = perturbed_violations[constraint_name][constraint_year]
                            derivative = (perturbed_val - current_val) / epsilon
                            
                            if abs(derivative) > 1e-10:  # Only store non-zero entries
                                jacobian[constraint_idx, var_idx] = derivative
                        
                        constraint_idx += 1
                
                var_idx += 1
    
    return jacobian.tocsr()
```

### 5. Implement Block Coordination

**Location**: `hierarchical_optimizer.py` - Add new method

**Required Implementation**:
```python
def _solve_with_blocks(self, objective_func, x_current, slack_current, years):
    """Coordinate block solving with Augmented Lagrangian"""
    
    current_deflators = self._vector_to_deflators(x_current, years)
    
    # Outer loop for block coordination
    for block_iter in range(self.block_solver.max_block_iterations):
        prev_deflators = deepcopy(current_deflators)
        
        # Solve each block sequentially
        block_solutions = self.block_solver.solve_blocks(
            current_deflators, years, self.lagrange_multipliers
        )
        
        # Update deflators from block solutions
        for block_name, solution in block_solutions.items():
            if solution.success:
                for deflator_name, value in solution.deflator_values.items():
                    for year in years:
                        current_deflators[deflator_name][year] = value
        
        # Check block convergence
        max_change = 0.0
        for deflator_name in current_deflators:
            for year in years:
                old_val = prev_deflators.get(deflator_name, {}).get(year, 100.0)
                new_val = current_deflators[deflator_name][year]
                change = abs(new_val - old_val) / max(abs(old_val), 1.0)
                max_change = max(max_change, change)
        
        if max_change < self.block_solver.block_tolerance:
            logger.info(f"Block coordination converged in {block_iter + 1} iterations")
            break
    
    # Convert back to vector
    x_result = self._deflators_to_vector(current_deflators, years)
    
    # Optimize slack variables separately if needed
    if len(slack_current) > 0:
        slack_result = self._optimize_slack_variables(x_result, slack_current, years)
        return np.concatenate([x_result, slack_result])
    
    return x_result
```

### 6. Add Target Deviation Calculation

**Location**: `hierarchical_optimizer.py` - Add new method

**Required Implementation**:
```python
def _calculate_target_deviations(self, years):
    """Calculate weighted sum of squared deviations from IMF targets"""
    total_deviation = 0.0
    
    # GDP targets (highest weight)
    gdp_targets = self.targets.get_gdp_targets(years)
    for year in years:
        if year in gdp_targets:
            target = gdp_targets[year]
            exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
            gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 0)
            
            if exchange_rate > 0 and target > 0:
                achieved_usd = gdp_nominal / (exchange_rate * 1000)
                deviation = ((achieved_usd - target) / target) ** 2
                total_deviation += 10.0 * deviation  # Weight = 10
    
    # Trade targets (medium weight)
    trade_targets = self.targets.get_trade_targets(years)
    for year in years:
        if 'imports' in trade_targets and year in trade_targets['imports']:
            target = trade_targets['imports'][year]
            exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
            imports_nominal = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)
            
            if exchange_rate > 0 and target > 0:
                achieved_usd = imports_nominal / (exchange_rate * 1000)
                deviation = ((achieved_usd - target) / target) ** 2
                total_deviation += 5.0 * deviation  # Weight = 5
    
    # Fiscal targets (medium weight)
    fiscal_targets = self.targets.get_fiscal_targets(years)
    for target_type in ['revenue_percent_gdp', 'expenditure_percent_gdp', 'balance_percent_gdp']:
        if target_type in fiscal_targets:
            for year in years:
                if year in fiscal_targets[target_type]:
                    target = fiscal_targets[target_type][year]
                    achieved = self._calculate_fiscal_indicator(target_type, year)
                    
                    if target != 0:
                        deviation = ((achieved - target) / abs(target)) ** 2
                        total_deviation += 3.0 * deviation  # Weight = 3
    
    return total_deviation
```

## Testing Requirements

After implementing these fixes:

1. **Verify Augmented Lagrangian**:
   - Check that constraint violations decrease with iterations
   - Verify Lagrange multipliers converge
   - Ensure penalty parameter adaptation works

2. **Test Block Coordination**:
   - Verify blocks solve independently
   - Check that multipliers coordinate blocks properly
   - Ensure convergence across blocks

3. **Validate Constraint System**:
   - Test that all 13 identities are evaluated correctly
   - Verify Jacobian computation accuracy
   - Check sparse matrix efficiency

4. **Integration Testing**:
   - Run on Yemen data for 2022-2025
   - Compare results with sequential approach
   - Verify all identities maintained within tolerance

## Success Criteria

Your implementation is successful when:
1. The optimizer uses actual Augmented Lagrangian method (not just scipy fallback)
2. Block coordinate descent properly coordinates optimization
3. All constraint evaluations work correctly
4. Convergence is achieved within reasonable iterations
5. Economic identities are maintained within 5% tolerance
6. IMF targets are better achieved than sequential method

## Important Notes

- Maintain backward compatibility with existing API
- Keep all error handling and fallback mechanisms
- Update logging to show optimization progress
- Document any significant algorithm choices
- Ensure numerical stability in all computations

Good luck implementing these critical mathematical components!