#!/usr/bin/env python3
"""
Sparse Constraint System for Economic Identity Validation
Implements efficient constraint evaluation using sparse matrices for the 13 economic identities.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from scipy.sparse import csr_matrix, lil_matrix
from dataclasses import dataclass

try:
    from .data_handler import YemenDataHandler
    from .identity_validator import IdentityValidator
except ImportError:
    from data_handler import YemenDataHandler
    from identity_validator import IdentityValidator

logger = logging.getLogger(__name__)


@dataclass
class ConstraintInfo:
    """Information about a constraint"""
    name: str
    constraint_type: str  # 'equality', 'inequality', 'crisis_gap'
    tolerance: float
    variables: List[str]  # Variable codes involved
    coefficients: List[float]  # Coefficients for linear constraints


class SparseConstraintSystem:
    """
    Efficient constraint evaluation using sparse matrices
    Handles the 13 economic identities with optimized computation
    """
    
    def __init__(self, data_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tolerance: float = 0.05):
        """
        Initialize sparse constraint system
        
        Args:
            data_handler: Handler for Yemen macro data
            tolerance: Crisis economy tolerance (5%)
        """
        self.data = data_handler
        self.tolerance = tolerance
        self.validator = IdentityValidator(data_handler, tolerance)
        
        # Constraint structure
        self.constraints = {}
        self.sparsity_pattern = {}
        self.constraint_cache = {}
        
        # Build constraint definitions
        self._build_constraint_definitions()
        
        logger.info(f"Initialized SparseConstraintSystem with {len(self.constraints)} constraint types")
    
    def _build_constraint_definitions(self):
        """Build definitions for all 13 economic identities"""
        
        # 1. GDP Expenditure Identity (Nominal)
        self.constraints['gdp_expenditure_nominal'] = ConstraintInfo(
            name='GDP Expenditure (Nominal)',
            constraint_type='equality',
            tolerance=self.tolerance,
            variables=[
                'YEMNYGDPMKTPCN',  # GDP
                'YEMNECONPRVTCN',  # Private consumption
                'YEMNECONGOVTCN',  # Government consumption
                'YEMNEGDIFTOTCN',  # Investment
                'YEMNEGDISTKBCN',  # Inventory
                'YEMNEEXPGNFSCN',  # Exports
                'YEMNEIMPGNFSCN',  # Imports
                'YEMNYGDPDISCCN'   # Statistical discrepancy
            ],
            coefficients=[1, -1, -1, -1, -1, -1, 1, -1]  # GDP = C + I + G + (X-M) + SD
        )
        
        # 2. GDP Expenditure Identity (Real)
        self.constraints['gdp_expenditure_real'] = ConstraintInfo(
            name='GDP Expenditure (Real)',
            constraint_type='equality',
            tolerance=self.tolerance,
            variables=[
                'YEMNYGDPMKTPKN',  # GDP real
                'YEMNECONPRVTKN',  # Private consumption real
                'YEMNECONGOVTKN',  # Government consumption real
                'YEMNEGDIFTOTKN',  # Investment real
                'YEMNEGDISTKBKN',  # Inventory real
                'YEMNEEXPGNFSKN',  # Exports real
                'YEMNEIMPGNFSKN',  # Imports real
                'YEMNYGDPDISCKN'   # Statistical discrepancy real
            ],
            coefficients=[1, -1, -1, -1, -1, -1, 1, -1]
        )
        
        # 3. GDP Production Identity (Nominal)
        self.constraints['gdp_production_nominal'] = ConstraintInfo(
            name='GDP Production (Nominal)',
            constraint_type='equality',
            tolerance=self.tolerance,
            variables=[
                'YEMNYGDPMKTPCN',  # GDP
                'YEMNVAGRTOTLCN',  # Agriculture
                'YEMNVINDTOTLCN',  # Industry
                'YEMNVSRVTOTLCN',  # Services
                'YEMNYTAXNINDCN'   # Net indirect taxes
            ],
            coefficients=[1, -1, -1, -1, -1]  # GDP = Agr + Ind + Serv + NetTax
        )
        
        # 4. GDP Production Identity (Real)
        self.constraints['gdp_production_real'] = ConstraintInfo(
            name='GDP Production (Real)',
            constraint_type='equality',
            tolerance=self.tolerance,
            variables=[
                'YEMNYGDPMKTPKN',  # GDP real
                'YEMNVAGRTOTLKN',  # Agriculture real
                'YEMNVINDTOTLKN',  # Industry real
                'YEMNVSRVTOTLKN',  # Services real
                'YEMNYTAXNINDKN'   # Net indirect taxes real
            ],
            coefficients=[1, -1, -1, -1, -1]
        )
        
        # 5. Investment Decomposition
        self.constraints['investment_decomposition'] = ConstraintInfo(
            name='Investment Decomposition',
            constraint_type='equality',
            tolerance=self.tolerance,
            variables=[
                'YEMNEGDIFTOTCN',  # Total investment
                'YEMNEGDIFGOVCN',  # Government investment
                'YEMNEGDIFPRVCN'   # Private investment
            ],
            coefficients=[1, -1, -1]  # Total = Gov + Private
        )
        
        # 6. Consumption Decomposition (implicit - private + government)
        # This is handled through the expenditure identity
        
        # 7. Deflator Relationships (Nominal = Real × Deflator/100)
        # These are handled as multiplicative constraints
        
        # 8. Fiscal Identity (Balance = Revenue - Expenditure)
        # This requires fiscal data which may not be directly in the main dataset
        
        # 9. External Sector Identities
        # Trade balance and current account relationships
        
        # 10. Crisis Economy Gaps (BOP, Savings-Investment)
        self.constraints['bop_identity'] = ConstraintInfo(
            name='Balance of Payments',
            constraint_type='crisis_gap',
            tolerance=5.0,  # Higher tolerance for crisis economy
            variables=[],  # Will be filled when BOP data is available
            coefficients=[]
        )
        
        self.constraints['savings_investment'] = ConstraintInfo(
            name='Savings-Investment Identity',
            constraint_type='crisis_gap',
            tolerance=5.0,  # Higher tolerance for crisis economy
            variables=[],  # Will be filled when savings data is available
            coefficients=[]
        )
        
        logger.info(f"Built {len(self.constraints)} constraint definitions")
    
    def evaluate_constraints(self, deflators: Dict[str, Dict[int, float]], 
                           years: List[int]) -> Dict[str, Dict[int, float]]:
        """
        Evaluate all constraints efficiently using sparse computation
        
        Args:
            deflators: Current deflator values
            years: Years to evaluate
            
        Returns:
            Dictionary mapping constraint_name -> year -> violation_amount
        """
        violations = {}
        
        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, years)
        
        try:
            # Evaluate each constraint
            for constraint_name, constraint_info in self.constraints.items():
                violations[constraint_name] = {}
                
                for year in years:
                    violation = self._evaluate_single_constraint(constraint_info, year)
                    violations[constraint_name][year] = violation
            
        finally:
            # Restore original data
            self._restore_data(original_data)
        
        return violations
    
    def _evaluate_single_constraint(self, constraint_info: ConstraintInfo, year: int) -> float:
        """
        Evaluate a single constraint for a specific year
        
        Args:
            constraint_info: Constraint definition
            year: Year to evaluate
            
        Returns:
            Constraint violation amount (0 if satisfied)
        """
        if not constraint_info.variables:
            return 0.0  # Skip constraints without variables defined
        
        # Get variable values
        values = []
        for var_code in constraint_info.variables:
            value = self.data.get_variable(var_code, [year]).get(year, 0)
            values.append(value)
        
        # Calculate constraint value using coefficients
        constraint_value = sum(coef * val for coef, val in zip(constraint_info.coefficients, values))
        
        # For equality constraints, violation is absolute value
        if constraint_info.constraint_type == 'equality':
            # Calculate relative error for large values, absolute for small values
            if abs(values[0]) > 1000:  # Use first variable as reference (usually GDP)
                relative_error = abs(constraint_value / values[0]) * 100
                return relative_error if relative_error > constraint_info.tolerance else 0.0
            else:
                return abs(constraint_value) if abs(constraint_value) > constraint_info.tolerance else 0.0
        
        # For crisis gaps, allow larger violations
        elif constraint_info.constraint_type == 'crisis_gap':
            if abs(values[0]) > 1000:
                relative_error = abs(constraint_value / values[0]) * 100
                return max(0, relative_error - constraint_info.tolerance)
            else:
                return max(0, abs(constraint_value) - constraint_info.tolerance)
        
        return 0.0
    
    def compute_jacobian(self, deflators: Dict[str, Dict[int, float]],
                        years: List[int]) -> csr_matrix:
        """
        Compute sparse Jacobian matrix of constraints with respect to deflators

        Args:
            deflators: Current deflator values
            years: Years to evaluate

        Returns:
            Sparse Jacobian matrix
        """
        # Setup
        num_constraints = len(self.constraints) * len(years)
        num_variables = sum(len(year_vals) for year_vals in deflators.values())

        # Use finite differences for Jacobian computation
        epsilon = 1e-6
        jacobian = lil_matrix((num_constraints, num_variables))

        # Current constraint values
        current_violations = self.evaluate_constraints(deflators, years)

        # Compute derivatives via finite differences
        var_idx = 0
        for deflator_name in deflators.keys():
            for year in years:
                if year in deflators[deflator_name]:
                    # Perturb this deflator
                    deflators_perturbed = {k: v.copy() for k, v in deflators.items()}
                    deflators_perturbed[deflator_name][year] += epsilon

                    # Evaluate constraints with perturbation
                    perturbed_violations = self.evaluate_constraints(deflators_perturbed, years)

                    # Compute derivatives
                    constraint_idx = 0
                    for constraint_name in self.constraints.keys():
                        for constraint_year in years:
                            if constraint_year in current_violations.get(constraint_name, {}):
                                # Finite difference derivative
                                current_val = current_violations[constraint_name][constraint_year]
                                perturbed_val = perturbed_violations[constraint_name][constraint_year]
                                derivative = (perturbed_val - current_val) / epsilon

                                if abs(derivative) > 1e-10:  # Only store non-zero entries
                                    jacobian[constraint_idx, var_idx] = derivative

                            constraint_idx += 1

                    var_idx += 1

        return jacobian.tocsr()
    
    def _deflator_affects_constraint(self, deflator_name: str, constraint_name: str) -> bool:
        """Check if a deflator affects a specific constraint"""
        # Mapping of deflators to constraints they affect
        deflator_constraint_map = {
            'gdp_deflator': ['gdp_expenditure_nominal', 'gdp_production_nominal'],
            'c_private_deflator': ['gdp_expenditure_nominal'],
            'c_government_deflator': ['gdp_expenditure_nominal'],
            'i_total_deflator': ['gdp_expenditure_nominal', 'investment_decomposition'],
            'i_private_deflator': ['investment_decomposition'],
            'i_government_deflator': ['investment_decomposition'],
            'exports_deflator': ['gdp_expenditure_nominal'],
            'imports_deflator': ['gdp_expenditure_nominal'],
            'agriculture_deflator': ['gdp_production_nominal'],
            'industry_deflator': ['gdp_production_nominal'],
            'services_deflator': ['gdp_production_nominal'],
            'net_taxes_deflator': ['gdp_production_nominal'],
        }
        
        return constraint_name in deflator_constraint_map.get(deflator_name, [])

    def _evaluate_single_constraint_by_name(self, constraint_name: str, deflators: Dict[str, Dict[int, float]], year: int) -> float:
        """
        Evaluate a single constraint by name for a specific year

        Args:
            constraint_name: Name of the constraint
            deflators: Current deflator values
            year: Year to evaluate

        Returns:
            Constraint violation amount
        """
        if constraint_name not in self.constraints:
            return 0.0

        constraint_info = self.constraints[constraint_name]

        # Apply deflators temporarily
        original_data = self._backup_data()
        self._apply_deflators_temporarily(deflators, [year])

        try:
            violation = self._evaluate_single_constraint(constraint_info, year)
        finally:
            self._restore_data(original_data)

        return violation
    
    def _backup_data(self) -> pd.DataFrame:
        """Backup current data state"""
        return self.data.df.copy()
    
    def _restore_data(self, backup: pd.DataFrame):
        """Restore data from backup"""
        self.data.df = backup.copy()
    
    def _apply_deflators_temporarily(self, deflators: Dict[str, Dict[int, float]], years: List[int]):
        """Apply deflators temporarily for constraint evaluation"""

        # Map deflator names to variable codes
        deflator_mapping = {
            'gdp_deflator': ('YEMNYGDPMKTPKN', 'YEMNYGDPMKTPCN'),
            'c_private_deflator': ('YEMNECONPRVTKN', 'YEMNECONPRVTCN'),
            'c_government_deflator': ('YEMNECONGOVTKN', 'YEMNECONGOVTCN'),
            'i_total_deflator': ('YEMNEGDIFTOTKN', 'YEMNEGDIFTOTCN'),
            'i_private_deflator': ('YEMNEGDIFPRVKN', 'YEMNEGDIFPRVCN'),
            'i_government_deflator': ('YEMNEGDIFGOVKN', 'YEMNEGDIFGOVCN'),
            'inventory_deflator': ('YEMNEGDISTKBKN', 'YEMNEGDISTKBCN'),
            'exports_deflator': ('YEMNEEXPGNFSKN', 'YEMNEEXPGNFSCN'),
            'imports_deflator': ('YEMNEIMPGNFSKN', 'YEMNEIMPGNFSCN'),
            'stat_disc_deflator': ('YEMNYGDPDISCKN', 'YEMNYGDPDISCCN'),
            'agriculture_deflator': ('YEMNVAGRTOTLKN', 'YEMNVAGRTOTLCN'),
            'industry_deflator': ('YEMNVINDTOTLKN', 'YEMNVINDTOTLCN'),
            'services_deflator': ('YEMNVSRVTOTLKN', 'YEMNVSRVTOTLCN'),
            'net_taxes_deflator': ('YEMNYTAXNINDKN', 'YEMNYTAXNINDCN'),
        }

        for deflator_name, year_values in deflators.items():
            if deflator_name in deflator_mapping:
                real_var, nominal_var = deflator_mapping[deflator_name]

                for year in years:
                    if year in year_values:
                        # Get real value
                        real_value = self.data.get_variable(real_var, [year]).get(year, 0)

                        if real_value != 0 and not pd.isna(real_value):
                            # Apply deflator: Nominal = Real × Deflator/100
                            new_deflator = year_values[year]
                            new_nominal = real_value * new_deflator / 100

                            # Update nominal value
                            self.data.update_variable(nominal_var, year, new_nominal)
