#!/usr/bin/env python3
"""
Hierarchical Optimizer for Simultaneous Deflator Adjustment
Implements sophisticated simultaneous optimization to maintain all economic identities
while achieving IMF targets through coordinated deflator adjustments.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass, field
from copy import deepcopy
import scipy.optimize as opt
from scipy.sparse import csr_matrix

try:
    from .data_handler import YemenDataHandler
    from .target_processor import IMFTargetProcessor
    from .identity_validator import IdentityValidator
    from .sparse_constraints import SparseConstraintSystem
    from .block_solver import BlockCoordinateDescent
    from .feasibility_manager import FeasibilityManager
    from .solver_enhancements import SolverEnhancements
except ImportError:
    from data_handler import YemenDataHandler
    from target_processor import IMFTargetProcessor
    from identity_validator import IdentityValidator
    from sparse_constraints import SparseConstraintSystem
    from block_solver import BlockCoordinateDescent
    from feasibility_manager import FeasibilityManager
    from solver_enhancements import SolverEnhancements

logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """Result of simultaneous optimization"""
    success: bool
    deflator_adjustments: Dict[str, Dict[int, float]]
    objective_value: float
    constraint_violations: Dict[str, float]
    iterations: int
    convergence_info: Dict[str, Any]
    target_achievement: Dict[str, Dict[int, float]]
    identity_validation: Dict[str, bool]
    error_messages: List[str] = field(default_factory=list)


class HierarchicalOptimizer:
    """
    Implements simultaneous optimization using hierarchical deflator structure
    with Augmented Lagrangian method and block coordinate descent.
    """
    
    def __init__(self, data_handler: YemenDataHandler, 
                 target_processor: IMFTargetProcessor,
                 tolerance: float = 0.05):
        """
        Initialize the hierarchical optimizer
        
        Args:
            data_handler: Handler for Yemen macro data
            target_processor: Processor for IMF targets
            tolerance: Crisis economy tolerance for identity violations (5%)
        """
        self.data = data_handler
        self.targets = target_processor
        self.tolerance = tolerance
        self.validator = IdentityValidator(data_handler, tolerance)
        
        # Build deflator hierarchy
        self.deflator_hierarchy = self._build_deflator_hierarchy()
        self.variable_mapping = self._build_variable_mapping()
        
        # Optimization parameters
        self.penalty_param = 100.0  # ρ for augmented Lagrangian - Start higher for crisis economy
        self.penalty_increase = 2.0  # Factor to increase penalty
        self.max_penalty = 1e6  # Maximum penalty parameter - Much higher for crisis constraints
        self.lagrange_multipliers = {}

        # Convergence parameters
        self.max_iterations = 1000
        self.constraint_tolerance = tolerance / 100 * 2  # More lenient for crisis economy (10% for 5% tolerance)
        self.objective_tolerance = 1e-6
        self.trust_region_radius = 1.0

        # Solution caching for warm-start strategy
        self.previous_solutions = {}  # Cache successful solutions
        self.solution_cache_size = 5  # Keep last 5 solutions

        # Anderson acceleration tracking
        self.x_history = []  # History of solution vectors
        self.f_history = []  # History of function evaluations
        self.anderson_memory = 5  # Memory depth for Anderson acceleration

        # Production diagnostics
        self.optimization_diagnostics = {
            'convergence_history': [],
            'constraint_violations_history': [],
            'penalty_parameter_history': [],
            'block_performance': {},
            'anderson_acceleration_count': 0,
            'trust_region_adjustments': 0
        }
        
        # Initialize constraint system (will be created when needed)
        self.constraint_system = None
        self.block_solver = None
        self.feasibility_manager = None
        self.solver_enhancements = None
        
        logger.info(f"Initialized HierarchicalOptimizer with {tolerance}% crisis tolerance")
    
    def _build_deflator_hierarchy(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Build 3-layer deflator hierarchy based on economic structure
        
        Returns:
            Dictionary with core, sectoral, and component deflator groups
        """
        hierarchy = {
            'core': {
                'gdp_aggregate': ['gdp_deflator'],
                'price_level': ['cpi_deflator']
            },
            'sectoral': {
                'production': ['agriculture_deflator', 'industry_deflator', 'services_deflator'],
                'expenditure': ['consumption_deflator', 'investment_deflator', 'government_deflator'],
                'external': ['exports_deflator', 'imports_deflator']
            },
            'component': {
                'consumption': ['c_private_deflator', 'c_government_deflator'],
                'investment': ['i_private_deflator', 'i_government_deflator', 'inventory_deflator'],
                'trade': ['exports_goods_deflator', 'exports_services_deflator', 
                         'imports_goods_deflator', 'imports_services_deflator'],
                'fiscal': ['revenue_deflator', 'expenditure_deflator'],
                'other': ['stat_disc_deflator', 'net_taxes_deflator']
            }
        }
        
        logger.info(f"Built deflator hierarchy with {len(hierarchy)} layers")
        return hierarchy
    
    def _build_variable_mapping(self) -> Dict[str, Dict[str, str]]:
        """
        Build mapping from deflator names to variable codes
        
        Returns:
            Dictionary mapping deflator names to (real_var, nominal_var) pairs
        """
        mapping = {
            # GDP and main aggregates
            'gdp_deflator': {'real': 'YEMNYGDPMKTPKN', 'nominal': 'YEMNYGDPMKTPCN'},
            
            # Expenditure components
            'c_private_deflator': {'real': 'YEMNECONPRVTKN', 'nominal': 'YEMNECONPRVTCN'},
            'c_government_deflator': {'real': 'YEMNECONGOVTKN', 'nominal': 'YEMNECONGOVTCN'},
            'i_total_deflator': {'real': 'YEMNEGDIFTOTKN', 'nominal': 'YEMNEGDIFTOTCN'},
            'i_private_deflator': {'real': 'YEMNEGDIFPRVKN', 'nominal': 'YEMNEGDIFPRVCN'},
            'i_government_deflator': {'real': 'YEMNEGDIFGOVKN', 'nominal': 'YEMNEGDIFGOVCN'},
            'inventory_deflator': {'real': 'YEMNEGDISTKBKN', 'nominal': 'YEMNEGDISTKBCN'},
            'exports_deflator': {'real': 'YEMNEEXPGNFSKN', 'nominal': 'YEMNEEXPGNFSCN'},
            'imports_deflator': {'real': 'YEMNEIMPGNFSKN', 'nominal': 'YEMNEIMPGNFSCN'},
            'stat_disc_deflator': {'real': 'YEMNYGDPDISCKN', 'nominal': 'YEMNYGDPDISCCN'},
            
            # Production sectors
            'agriculture_deflator': {'real': 'YEMNVAGRTOTLKN', 'nominal': 'YEMNVAGRTOTLCN'},
            'industry_deflator': {'real': 'YEMNVINDTOTLKN', 'nominal': 'YEMNVINDTOTLCN'},
            'services_deflator': {'real': 'YEMNVSRVTOTLKN', 'nominal': 'YEMNVSRVTOTLCN'},
            'net_taxes_deflator': {'real': 'YEMNYTAXNINDKN', 'nominal': 'YEMNYTAXNINDCN'},
            'gdp_fc_deflator': {'real': 'YEMNYGDPFCSTKN', 'nominal': 'YEMNYGDPFCSTCN'},
        }
        
        logger.info(f"Built variable mapping for {len(mapping)} deflators")
        return mapping
    
    def optimize(self, years: List[int]) -> OptimizationResult:
        """
        Main optimization method using simultaneous approach
        
        Args:
            years: List of years to optimize
            
        Returns:
            OptimizationResult with success status and details
        """
        logger.info(f"Starting simultaneous optimization for years {years}")
        
        try:
            # Initialize constraint system and solvers
            self._initialize_solvers()
            
            # Get initial deflator values with warm-start if available
            warm_start_deflators = self._get_warm_start_deflators(years)
            if warm_start_deflators:
                logger.info("Using warm-start from previous solution")
                initial_deflators = warm_start_deflators
            else:
                initial_deflators = self._get_current_deflators(years)
            
            # Set up optimization problem
            bounds, constraints = self._setup_optimization_problem(years)
            
            # Initialize Lagrange multipliers
            self._initialize_lagrange_multipliers(years)
            
            # Run Augmented Lagrangian optimization
            result = self._run_augmented_lagrangian(
                initial_deflators, years, bounds, constraints
            )
            
            if result.success:
                # Cache successful solution for future warm-starts
                self._cache_solution(result.deflator_adjustments, years)

                # Apply optimal deflator adjustments
                self._apply_deflator_solution(result.deflator_adjustments, years)

                # Validate final state
                final_validation = self.validator.validate_all(years, critical_only=False)
                target_achievement = self._calculate_target_achievement(years)
                
                logger.info("✅ Simultaneous optimization completed successfully")
                return OptimizationResult(
                    success=True,
                    deflator_adjustments=result.deflator_adjustments,
                    objective_value=result.objective_value,
                    constraint_violations=result.constraint_violations,
                    iterations=result.iterations,
                    convergence_info=result.convergence_info,
                    target_achievement=target_achievement,
                    identity_validation=final_validation
                )
            else:
                logger.error("❌ Simultaneous optimization failed")
                return OptimizationResult(
                    success=False,
                    deflator_adjustments={},
                    objective_value=float('inf'),
                    constraint_violations={},
                    iterations=result.iterations,
                    convergence_info=result.convergence_info,
                    target_achievement={},
                    identity_validation={},
                    error_messages=result.error_messages
                )
                
        except Exception as e:
            error_msg = f"Unexpected error in simultaneous optimization: {str(e)}"
            logger.error(error_msg)
            return OptimizationResult(
                success=False,
                deflator_adjustments={},
                objective_value=float('inf'),
                constraint_violations={},
                iterations=0,
                convergence_info={},
                target_achievement={},
                identity_validation={},
                error_messages=[error_msg]
            )
    
    def _initialize_solvers(self):
        """Initialize constraint system and block solvers"""
        try:
            # Import here to avoid circular dependencies
            try:
                from .sparse_constraints import SparseConstraintSystem
                from .block_solver import BlockCoordinateDescent
                from .feasibility_manager import FeasibilityManager
            except ImportError:
                # Fallback to absolute imports
                from sparse_constraints import SparseConstraintSystem
                from block_solver import BlockCoordinateDescent
                from feasibility_manager import FeasibilityManager

            self.constraint_system = SparseConstraintSystem(self.data)
            self.block_solver = BlockCoordinateDescent(self.data, self.targets)
            self.feasibility_manager = FeasibilityManager(crisis_tolerance=self.tolerance)
            self.solver_enhancements = SolverEnhancements(tolerance=self.constraint_tolerance)

            logger.info("Initialized constraint system, block solvers, and solver enhancements")
        except ImportError as e:
            logger.warning(f"Advanced solvers not available, using basic implementation: {e}")
            # Use basic implementations
            self.constraint_system = None
            self.block_solver = None
            self.feasibility_manager = None
    
    def _get_current_deflators(self, years: List[int]) -> Dict[str, Dict[int, float]]:
        """
        Get current deflator values for all variables and years
        
        Args:
            years: List of years
            
        Returns:
            Dictionary mapping deflator_name -> year -> current_deflator_value
        """
        current_deflators = {}
        
        for deflator_name, var_codes in self.variable_mapping.items():
            current_deflators[deflator_name] = {}
            
            for year in years:
                real_value = self.data.get_variable(var_codes['real'], [year]).get(year, 0)
                nominal_value = self.data.get_variable(var_codes['nominal'], [year]).get(year, 0)
                
                if real_value != 0 and not pd.isna(real_value) and not pd.isna(nominal_value):
                    deflator = (nominal_value / real_value) * 100
                    current_deflators[deflator_name][year] = deflator
                else:
                    current_deflators[deflator_name][year] = 100.0  # Default deflator
        
        logger.info(f"Retrieved current deflators for {len(current_deflators)} variables")
        return current_deflators

    def _setup_optimization_problem(self, years: List[int]) -> Tuple[List[Tuple[float, float]], Dict]:
        """
        Set up optimization problem bounds and constraints

        Args:
            years: List of years to optimize

        Returns:
            Tuple of (bounds, constraints) for optimization
        """
        bounds = []
        constraints = {}

        # Set deflator bounds (20% to 200% of current values)
        for deflator_name in self.variable_mapping.keys():
            for year in years:
                # Reasonable bounds for deflators in crisis economy
                bounds.append((20.0, 200.0))  # 20% to 200%

        # Set up constraint structure (will be filled by constraint system)
        constraints = {
            'identity_constraints': [],
            'target_constraints': [],
            'crisis_gaps': []
        }

        logger.info(f"Set up optimization problem with {len(bounds)} variables")
        return bounds, constraints

    def _initialize_lagrange_multipliers(self, years: List[int]):
        """Initialize Lagrange multipliers for constraints"""
        self.lagrange_multipliers = {
            'identity_constraints': {},
            'target_constraints': {},
            'crisis_gaps': {}
        }

        # Initialize multipliers for each identity constraint
        identity_names = [
            'gdp_expenditure_nominal', 'gdp_expenditure_real',
            'gdp_production_nominal', 'gdp_production_real',
            'deflator_relationships', 'investment_decomposition',
            'consumption_decomposition', 'fiscal_identities',
            'external_sector', 'gdp_fiscal_consistency',
            'trade_consistency'
        ]

        for identity in identity_names:
            self.lagrange_multipliers['identity_constraints'][identity] = {
                year: 0.0 for year in years
            }

        logger.info(f"Initialized Lagrange multipliers for {len(identity_names)} identities")

    def _run_augmented_lagrangian(self, initial_deflators: Dict, years: List[int],
                                 bounds: List, constraints: Dict) -> OptimizationResult:
        """
        Run Augmented Lagrangian optimization with block coordinate descent

        Args:
            initial_deflators: Initial deflator values
            years: List of years
            bounds: Variable bounds
            constraints: Constraint definitions

        Returns:
            OptimizationResult with optimization details
        """
        logger.info("Starting Augmented Lagrangian optimization")

        # Initialize
        x_current = self._deflators_to_vector(initial_deflators, years)
        slack_current = np.zeros(len(self.feasibility_manager.slack_variables) * len(years) if self.feasibility_manager else 0)

        prev_max_violation = float('inf')

        # Initialize Anderson acceleration tracking
        self.x_history = []
        self.f_history = []

        for outer_iter in range(self.max_iterations):
            # Step 1: Minimize augmented Lagrangian with current multipliers
            def augmented_objective(x_and_slack):
                x = x_and_slack[:len(x_current)]
                slack = x_and_slack[len(x_current):] if len(x_and_slack) > len(x_current) else np.array([])

                # Apply deflators and calculate targets
                deflators = self._vector_to_deflators(x, years)

                # Backup and apply deflators temporarily
                original_data = self._backup_data()
                self._apply_deflators_temporarily(deflators, years)

                try:
                    # f(x): Target deviations
                    target_dev = self._calculate_target_deviations(years)

                    # h(x): Constraint violations
                    violations = self.constraint_system.evaluate_constraints(deflators, years)

                    # Augmented Lagrangian terms
                    penalty_term = 0.0
                    multiplier_term = 0.0

                    for constraint_name, year_violations in violations.items():
                        for year, violation in year_violations.items():
                            # λᵀh(x)
                            if 'identity_constraints' in self.lagrange_multipliers:
                                if constraint_name in self.lagrange_multipliers['identity_constraints']:
                                    if year in self.lagrange_multipliers['identity_constraints'][constraint_name]:
                                        λ = self.lagrange_multipliers['identity_constraints'][constraint_name][year]
                                        multiplier_term += λ * violation

                            # (ρ/2)||h(x)||²
                            penalty_term += (self.penalty_param / 2) * violation**2

                    # μ||ξ||₁ for slack variables
                    slack_penalty = 0.0
                    if len(slack) > 0 and self.feasibility_manager:
                        slack_dict = self._vector_to_slack_dict(slack, years)
                        slack_penalty = self.feasibility_manager.get_penalty_term(slack_dict)

                    return target_dev + multiplier_term + penalty_term + slack_penalty

                finally:
                    # Restore original data
                    self._restore_data(original_data)

            # Step 2: Use block coordinate descent or fallback to scipy
            if self.block_solver:
                result_x = self._solve_with_blocks(augmented_objective, x_current, slack_current, years)
                if len(result_x) > len(x_current):
                    x_new = result_x[:len(x_current)]
                    slack_current = result_x[len(x_current):]
                else:
                    x_new = result_x

                # Apply Anderson acceleration if we have enough history
                if self.solver_enhancements and len(self.x_history) >= 2:
                    try:
                        x_accelerated = self.solver_enhancements.anderson_accelerate(
                            self.x_history,
                            self.f_history,
                            m=self.anderson_memory
                        )
                        # Use accelerated point if it's reasonable
                        if np.all(np.isfinite(x_accelerated)) and np.all(x_accelerated > 10) and np.all(x_accelerated < 300):
                            x_current = x_accelerated
                            self.optimization_diagnostics['anderson_acceleration_count'] += 1
                            logger.debug("✨ Applied Anderson acceleration")
                        else:
                            x_current = x_new
                            logger.debug("⚠️ Anderson acceleration produced invalid point, using standard step")
                    except Exception as e:
                        logger.warning(f"Anderson acceleration failed: {e}")
                        x_current = x_new
                else:
                    x_current = x_new

                # Update history for Anderson acceleration
                self.x_history.append(x_current.copy())
                self.f_history.append(x_new.copy())  # Store the unaccelerated result as f(x)

                # Keep only recent history
                if len(self.x_history) > self.anderson_memory + 1:
                    self.x_history.pop(0)
                    self.f_history.pop(0)
            else:
                # Fallback to scipy
                x_and_slack = np.concatenate([x_current, slack_current]) if len(slack_current) > 0 else x_current
                slack_bounds = [(-10.0, 10.0)] * len(slack_current) if len(slack_current) > 0 else []

                result = opt.minimize(
                    augmented_objective,
                    x_and_slack,
                    method='L-BFGS-B',
                    bounds=bounds + slack_bounds
                )

                if result.success:
                    if len(slack_current) > 0:
                        x_current = result.x[:len(x_current)]
                        slack_current = result.x[len(x_current):]
                    else:
                        x_current = result.x

            # Step 3: Update Lagrange multipliers
            violations = self.constraint_system.evaluate_constraints(
                self._vector_to_deflators(x_current, years), years
            )

            if self.block_solver:
                self.lagrange_multipliers = self.block_solver.update_multipliers(
                    violations, self.lagrange_multipliers, self.penalty_param
                )

            # Step 4: Check convergence
            max_violation = max(
                abs(v) for year_dict in violations.values()
                for v in year_dict.values()
            ) if violations else 0.0

            # Record diagnostics
            self.optimization_diagnostics['convergence_history'].append({
                'iteration': outer_iter + 1,
                'max_violation': max_violation,
                'objective_value': augmented_objective(np.concatenate([x_current, slack_current]) if len(slack_current) > 0 else x_current),
                'penalty_parameter': self.penalty_param
            })

            self.optimization_diagnostics['constraint_violations_history'].append(violations.copy())
            self.optimization_diagnostics['penalty_parameter_history'].append(self.penalty_param)

            # Enhanced logging
            if outer_iter % 10 == 0 or max_violation < self.constraint_tolerance:
                logger.info(f"Iteration {outer_iter + 1}: max_violation={max_violation:.6f}, penalty_param={self.penalty_param:.2f}")

                # Log top constraint violations
                violation_list = [(name, year, abs(v)) for name, year_dict in violations.items()
                                for year, v in year_dict.items()]
                violation_list.sort(key=lambda x: x[2], reverse=True)

                for i, (name, year, violation) in enumerate(violation_list[:3]):
                    logger.info(f"  Top violation {i+1}: {name}[{year}] = {violation:.2f}%")

            if max_violation < self.constraint_tolerance:
                logger.info(f"✅ Converged after {outer_iter + 1} iterations")
                break

            # Step 5: Update penalty parameter more aggressively for crisis economy
            if outer_iter > 0:
                if max_violation > prev_max_violation * 0.5:  # More aggressive threshold
                    self.penalty_param = min(self.penalty_param * 10.0, self.max_penalty)  # Faster increase
                    logger.info(f"Aggressively increased penalty parameter to {self.penalty_param}")
                elif max_violation > self.constraint_tolerance * 10:  # Still too high
                    self.penalty_param = min(self.penalty_param * 2.0, self.max_penalty)
                    logger.info(f"Moderately increased penalty parameter to {self.penalty_param}")

            prev_max_violation = max_violation

        # Return result
        optimal_deflators = self._vector_to_deflators(x_current, years)
        final_objective = augmented_objective(np.concatenate([x_current, slack_current]) if len(slack_current) > 0 else x_current)

        return OptimizationResult(
            success=True,
            deflator_adjustments=optimal_deflators,
            objective_value=final_objective,
            constraint_violations=violations,
            iterations=outer_iter + 1,
            convergence_info={'max_violation': max_violation, 'penalty_param': self.penalty_param},
            target_achievement={},
            identity_validation={}
        )

    def _deflators_to_vector(self, deflators: Dict[str, Dict[int, float]], years: List[int]) -> np.ndarray:
        """Convert deflator dictionary to flat vector for optimization"""
        vector = []
        for deflator_name in self.variable_mapping.keys():
            for year in years:
                vector.append(deflators.get(deflator_name, {}).get(year, 100.0))
        return np.array(vector)

    def _vector_to_deflators(self, vector: np.ndarray, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Convert flat vector back to deflator dictionary"""
        deflators = {}
        idx = 0
        for deflator_name in self.variable_mapping.keys():
            deflators[deflator_name] = {}
            for year in years:
                deflators[deflator_name][year] = vector[idx]
                idx += 1
        return deflators

    def _run_basic_optimization(self, x0: np.ndarray, years: List[int], bounds: List) -> Any:
        """Run basic scipy optimization as fallback"""
        logger.info("Using basic scipy optimization (fallback mode)")

        def objective_function(x):
            """Objective function: weighted sum of squared target deviations"""
            deflators = self._vector_to_deflators(x, years)

            # Apply deflators temporarily to calculate targets
            original_data = self._backup_data()
            self._apply_deflators_temporarily(deflators, years)

            # Calculate target deviations
            total_deviation = 0.0

            # GDP targets (high weight)
            gdp_targets = self.targets.get_gdp_targets(years)
            for year in years:
                if year in gdp_targets:
                    target = gdp_targets[year]
                    exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
                    gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 0)

                    if exchange_rate > 0 and target > 0:
                        achieved_usd = gdp_nominal / (exchange_rate * 1000)  # Convert to billions USD
                        deviation = ((achieved_usd - target) / target) ** 2
                        total_deviation += 10.0 * deviation  # High weight for GDP

            # Trade targets (medium weight)
            trade_targets = self.targets.get_trade_targets(years)
            for year in years:
                # Import targets
                if 'imports' in trade_targets and year in trade_targets['imports']:
                    target = trade_targets['imports'][year]
                    exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
                    imports_nominal = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

                    if exchange_rate > 0 and target > 0:
                        achieved_usd = imports_nominal / (exchange_rate * 1000)
                        deviation = ((achieved_usd - target) / target) ** 2
                        total_deviation += 5.0 * deviation  # Medium weight

            # Restore original data
            self._restore_data(original_data)

            return total_deviation

        # Run optimization
        result = opt.minimize(
            objective_function,
            x0,
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': 1000, 'ftol': 1e-6}
        )

        return result

    def _run_block_coordinate_descent(self, x0: np.ndarray, years: List[int], bounds: List) -> Any:
        """Run block coordinate descent optimization (placeholder)"""
        logger.info("Block coordinate descent not yet implemented, using basic optimization")
        return self._run_basic_optimization(x0, years, bounds)

    def _calculate_target_deviations(self, years: List[int]) -> float:
        """Calculate weighted sum of squared deviations from IMF targets"""
        total_deviation = 0.0

        # GDP targets (highest weight)
        gdp_targets = self.targets.get_gdp_targets(years)
        for year in years:
            if year in gdp_targets:
                target = gdp_targets[year]
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
                gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 0)

                if exchange_rate > 0 and target > 0:
                    achieved_usd = gdp_nominal / (exchange_rate * 1000)
                    deviation = ((achieved_usd - target) / target) ** 2
                    total_deviation += 10.0 * deviation  # Weight = 10

        # Trade targets (medium weight)
        trade_targets = self.targets.get_trade_targets(years)
        for year in years:
            if 'imports' in trade_targets and year in trade_targets['imports']:
                target = trade_targets['imports'][year]
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
                imports_nominal = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

                if exchange_rate > 0 and target > 0:
                    achieved_usd = imports_nominal / (exchange_rate * 1000)
                    deviation = ((achieved_usd - target) / target) ** 2
                    total_deviation += 5.0 * deviation  # Weight = 5

        # Fiscal targets (medium weight)
        fiscal_targets = self.targets.get_fiscal_targets(years)
        for target_type in ['revenue_percent_gdp', 'expenditure_percent_gdp', 'balance_percent_gdp']:
            if target_type in fiscal_targets:
                for year in years:
                    if year in fiscal_targets[target_type]:
                        target = fiscal_targets[target_type][year]
                        achieved = self._calculate_fiscal_indicator(target_type, year)

                        if target != 0:
                            deviation = ((achieved - target) / abs(target)) ** 2
                            total_deviation += 3.0 * deviation  # Weight = 3

        return total_deviation

    def _calculate_fiscal_indicator(self, indicator_type: str, year: int) -> float:
        """Calculate fiscal indicator for a specific year"""
        # This is a simplified implementation - would need actual fiscal data
        gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 1)

        if indicator_type == 'revenue_percent_gdp':
            # Placeholder - would need actual revenue data
            return 15.0  # Assume 15% of GDP
        elif indicator_type == 'expenditure_percent_gdp':
            # Placeholder - would need actual expenditure data
            return 20.0  # Assume 20% of GDP
        elif indicator_type == 'balance_percent_gdp':
            # Deficit = expenditure - revenue
            return -5.0  # Assume 5% deficit

        return 0.0

    def _vector_to_slack_dict(self, slack_vector: np.ndarray, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Convert slack vector to dictionary format"""
        slack_dict = {}
        idx = 0

        if self.feasibility_manager and hasattr(self.feasibility_manager, 'slack_variables'):
            for constraint_name in self.feasibility_manager.slack_variables:
                slack_dict[constraint_name] = {}
                for year in years:
                    if idx < len(slack_vector):
                        slack_dict[constraint_name][year] = slack_vector[idx]
                        idx += 1

        return slack_dict

    def _optimize_slack_variables(self, x_deflators: np.ndarray, slack_current: np.ndarray, years: List[int]) -> np.ndarray:
        """Optimize slack variables separately if needed"""
        # For now, return current slack values
        # In a full implementation, this would solve a sub-problem for slack variables
        return slack_current

    def _get_warm_start_deflators(self, years: List[int]) -> Optional[Dict]:
        """Get warm-start from previous solutions"""
        if not self.previous_solutions:
            return None

        # Check cache for similar year ranges
        for cached_years, solution in self.previous_solutions.items():
            if set(years).intersection(set(cached_years)):
                # Use overlapping years as warm start
                logger.info(f"Found warm-start solution for overlapping years: {cached_years}")
                return solution
        return None

    def _cache_solution(self, deflator_adjustments: Dict, years: List[int]):
        """Cache successful solution for future warm-starts"""
        # Convert years list to tuple for use as dict key
        years_key = tuple(sorted(years))

        # Add to cache
        self.previous_solutions[years_key] = deflator_adjustments.copy()

        # Maintain cache size limit
        if len(self.previous_solutions) > self.solution_cache_size:
            # Remove oldest entry
            oldest_key = min(self.previous_solutions.keys())
            del self.previous_solutions[oldest_key]

        logger.info(f"Cached solution for years {years}")

    def _solve_with_blocks(self, objective_func, x_current: np.ndarray, slack_current: np.ndarray, years: List[int]) -> np.ndarray:
        """Coordinate block solving with Augmented Lagrangian"""

        current_deflators = self._vector_to_deflators(x_current, years)

        # Outer loop for block coordination
        for block_iter in range(self.block_solver.max_block_iterations if self.block_solver else 10):
            prev_deflators = {k: v.copy() for k, v in current_deflators.items()}

            # Solve each block sequentially
            block_solutions = self.block_solver.solve_blocks(
                current_deflators, years, self.lagrange_multipliers
            )

            # Update deflators from block solutions
            for block_name, solution in block_solutions.items():
                if solution.success:
                    for deflator_name, value in solution.deflator_values.items():
                        for year in years:
                            if deflator_name not in current_deflators:
                                current_deflators[deflator_name] = {}
                            current_deflators[deflator_name][year] = value

            # Check block convergence
            max_change = 0.0
            for deflator_name in current_deflators:
                for year in years:
                    old_val = prev_deflators.get(deflator_name, {}).get(year, 100.0)
                    new_val = current_deflators[deflator_name][year]
                    change = abs(new_val - old_val) / max(abs(old_val), 1.0)
                    max_change = max(max_change, change)

            if max_change < (self.block_solver.block_tolerance if self.block_solver else 1e-4):
                logger.info(f"Block coordination converged in {block_iter + 1} iterations")
                break

        # Convert back to vector
        x_result = self._deflators_to_vector(current_deflators, years)

        # Optimize slack variables separately if needed
        if len(slack_current) > 0:
            slack_result = self._optimize_slack_variables(x_result, slack_current, years)
            return np.concatenate([x_result, slack_result])

        return x_result

    def _backup_data(self) -> pd.DataFrame:
        """Backup current data state"""
        return self.data.df.copy()

    def _restore_data(self, backup: pd.DataFrame):
        """Restore data from backup"""
        self.data.df = backup.copy()

    def _apply_deflators_temporarily(self, deflators: Dict[str, Dict[int, float]], years: List[int]):
        """Apply deflators temporarily for evaluation"""
        for deflator_name, var_codes in self.variable_mapping.items():
            if deflator_name in deflators:
                for year in years:
                    if year in deflators[deflator_name]:
                        real_value = self.data.get_variable(var_codes['real'], [year]).get(year, 0)
                        if real_value != 0 and not pd.isna(real_value):
                            new_deflator = deflators[deflator_name][year]
                            new_nominal = real_value * new_deflator / 100
                            self.data.update_variable(var_codes['nominal'], year, new_nominal)

    def _apply_deflator_solution(self, deflators: Dict[str, Dict[int, float]], years: List[int]):
        """Apply final deflator solution to data"""
        logger.info("Applying optimal deflator solution")

        adjustments_made = 0
        for deflator_name, var_codes in self.variable_mapping.items():
            if deflator_name in deflators:
                for year in years:
                    if year in deflators[deflator_name]:
                        real_value = self.data.get_variable(var_codes['real'], [year]).get(year, 0)
                        if real_value != 0 and not pd.isna(real_value):
                            new_deflator = deflators[deflator_name][year]
                            new_nominal = real_value * new_deflator / 100

                            # Update the data
                            success = self.data.update_variable(var_codes['nominal'], year, new_nominal)
                            if success:
                                adjustments_made += 1

        logger.info(f"Applied {adjustments_made} deflator adjustments")

    def _calculate_target_achievement(self, years: List[int]) -> Dict[str, Dict[int, float]]:
        """Calculate target achievement percentages"""
        achievement = {}

        # GDP target achievement
        gdp_targets = self.targets.get_gdp_targets(years)
        achievement['gdp_usd'] = {}

        for year in years:
            if year in gdp_targets:
                target = gdp_targets[year]
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
                gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 0)

                if exchange_rate > 0 and target > 0:
                    achieved_usd = gdp_nominal / (exchange_rate * 1000)
                    achievement['gdp_usd'][year] = (achieved_usd / target) * 100

        # Trade target achievement
        trade_targets = self.targets.get_trade_targets(years)
        achievement['imports_usd'] = {}

        for year in years:
            if 'imports' in trade_targets and year in trade_targets['imports']:
                target = trade_targets['imports'][year]
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)
                imports_nominal = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

                if exchange_rate > 0 and target > 0:
                    achieved_usd = imports_nominal / (exchange_rate * 1000)
                    achievement['imports_usd'][year] = (achieved_usd / target) * 100

        return achievement

    def generate_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive optimization diagnostics report"""

        if not self.optimization_diagnostics['convergence_history']:
            return {"error": "No optimization data available"}

        # Convergence analysis
        convergence_data = self.optimization_diagnostics['convergence_history']
        final_iteration = convergence_data[-1]

        # Calculate convergence rate
        if len(convergence_data) > 1:
            initial_violation = convergence_data[0]['max_violation']
            final_violation = final_iteration['max_violation']
            convergence_rate = (initial_violation - final_violation) / len(convergence_data)
        else:
            convergence_rate = 0.0

        # Constraint violation analysis
        violations_history = self.optimization_diagnostics['constraint_violations_history']
        if violations_history:
            final_violations = violations_history[-1]

            # Find most problematic constraints
            worst_violations = []
            for constraint_name, year_dict in final_violations.items():
                for year, violation in year_dict.items():
                    worst_violations.append({
                        'constraint': constraint_name,
                        'year': year,
                        'violation_percent': abs(violation)
                    })

            worst_violations.sort(key=lambda x: x['violation_percent'], reverse=True)
            worst_violations = worst_violations[:5]  # Top 5
        else:
            worst_violations = []

        # Performance metrics
        anderson_effectiveness = (
            self.optimization_diagnostics['anderson_acceleration_count'] /
            len(convergence_data) * 100 if convergence_data else 0
        )

        report = {
            'optimization_summary': {
                'total_iterations': len(convergence_data),
                'converged': final_iteration['max_violation'] < self.constraint_tolerance,
                'final_max_violation': final_iteration['max_violation'],
                'final_penalty_parameter': final_iteration['penalty_parameter'],
                'convergence_rate_per_iteration': convergence_rate
            },
            'constraint_analysis': {
                'worst_violations': worst_violations,
                'total_constraints_evaluated': len(final_violations) if violations_history else 0,
                'constraints_within_tolerance': sum(
                    1 for year_dict in final_violations.values()
                    for v in year_dict.values()
                    if abs(v) < self.constraint_tolerance * 100
                ) if violations_history else 0
            },
            'solver_performance': {
                'anderson_acceleration_applied': self.optimization_diagnostics['anderson_acceleration_count'],
                'anderson_effectiveness_percent': anderson_effectiveness,
                'trust_region_adjustments': self.optimization_diagnostics.get('trust_region_adjustments', 0),
                'warm_starts_used': len(self.previous_solutions)
            },
            'recommendations': self._generate_recommendations(convergence_data, worst_violations)
        }

        return report

    def _generate_recommendations(self, convergence_data: List[Dict], worst_violations: List[Dict]) -> List[str]:
        """Generate optimization recommendations based on performance"""
        recommendations = []

        if not convergence_data:
            return ["No convergence data available for analysis"]

        final_data = convergence_data[-1]

        # Convergence recommendations
        if final_data['max_violation'] > self.constraint_tolerance:
            recommendations.append("❌ Optimization did not converge. Consider:")
            recommendations.append("  • Increasing max_iterations")
            recommendations.append("  • Relaxing constraint_tolerance for crisis economy")
            recommendations.append("  • Increasing penalty parameter more aggressively")
        else:
            recommendations.append("✅ Optimization converged successfully")

        # Constraint-specific recommendations
        if worst_violations:
            recommendations.append(f"🎯 Focus on top constraint violations:")
            for i, violation in enumerate(worst_violations[:3]):
                recommendations.append(f"  {i+1}. {violation['constraint']} ({violation['violation_percent']:.1f}%)")

        # Performance recommendations
        anderson_count = self.optimization_diagnostics['anderson_acceleration_count']
        total_iterations = len(convergence_data)

        if anderson_count / total_iterations < 0.1:
            recommendations.append("⚡ Anderson acceleration rarely applied - check convergence criteria")
        elif anderson_count / total_iterations > 0.5:
            recommendations.append("✨ Anderson acceleration working well")

        # Parameter tuning recommendations
        penalty_history = self.optimization_diagnostics['penalty_parameter_history']
        if penalty_history and penalty_history[-1] >= self.max_penalty * 0.9:
            recommendations.append("⚠️ Penalty parameter near maximum - consider increasing max_penalty")

        return recommendations

    def print_optimization_summary(self):
        """Print a formatted optimization summary"""
        report = self.generate_optimization_report()

        if 'error' in report:
            print(f"❌ {report['error']}")
            return

        print("\n" + "="*60)
        print("🚀 SIMULTANEOUS OPTIMIZATION SUMMARY")
        print("="*60)

        summary = report['optimization_summary']
        print(f"📊 Iterations: {summary['total_iterations']}")
        print(f"✅ Converged: {'Yes' if summary['converged'] else 'No'}")
        print(f"📉 Final Max Violation: {summary['final_max_violation']:.6f}")
        print(f"⚡ Penalty Parameter: {summary['final_penalty_parameter']:.2f}")

        print(f"\n🎯 CONSTRAINT ANALYSIS")
        print("-"*30)
        constraint_analysis = report['constraint_analysis']
        print(f"📋 Total Constraints: {constraint_analysis['total_constraints_evaluated']}")
        print(f"✅ Within Tolerance: {constraint_analysis['constraints_within_tolerance']}")

        if constraint_analysis['worst_violations']:
            print(f"\n⚠️ TOP VIOLATIONS:")
            for i, violation in enumerate(constraint_analysis['worst_violations'][:3]):
                print(f"  {i+1}. {violation['constraint']}[{violation['year']}]: {violation['violation_percent']:.2f}%")

        print(f"\n🔧 SOLVER PERFORMANCE")
        print("-"*30)
        performance = report['solver_performance']
        print(f"✨ Anderson Acceleration: {performance['anderson_acceleration_applied']} times ({performance['anderson_effectiveness_percent']:.1f}%)")
        print(f"🎯 Trust Region Adjustments: {performance['trust_region_adjustments']}")
        print(f"🔄 Warm Starts Available: {performance['warm_starts_used']}")

        print(f"\n💡 RECOMMENDATIONS")
        print("-"*30)
        for rec in report['recommendations']:
            print(f"{rec}")

        print("="*60)
