#!/usr/bin/env python3
"""
Feasibility Manager for Crisis Economy Constraints
Handles over-constrained systems with L1-penalized slack variables and adaptive relaxation.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Set
import logging
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class SlackVariable:
    """Represents a slack variable for constraint relaxation"""
    constraint_name: str
    year: int
    penalty_weight: float
    current_value: float = 0.0
    bounds: Tuple[float, float] = (-float('inf'), float('inf'))


@dataclass
class FeasibilityReport:
    """Report on feasibility analysis and relaxations applied"""
    is_feasible: bool
    infeasible_constraints: List[str]
    slack_variables: Dict[str, Dict[int, float]]
    relaxation_penalties: Dict[str, float]
    total_penalty: float
    recommendations: List[str] = field(default_factory=list)


class FeasibilityManager:
    """
    Manages feasibility of over-constrained crisis economy systems
    """
    
    def __init__(self, crisis_tolerance: float = 5.0):
        """
        Initialize feasibility manager
        
        Args:
            crisis_tolerance: Base tolerance for crisis economy (5%)
        """
        self.crisis_tolerance = crisis_tolerance
        self.slack_variables = {}
        self.relaxation_penalties = {}
        
        # Define crisis-specific constraints that can be relaxed
        self.crisis_constraints = {
            'bop_identity': {
                'description': 'Balance of Payments Identity',
                'max_relaxation': 10.0,  # Up to 10% of GDP
                'penalty_weight': 1.0,
                'justification': 'Crisis economies often have BOP data inconsistencies'
            },
            'savings_investment': {
                'description': 'Savings-Investment Identity',
                'max_relaxation': 8.0,  # Up to 8% of GDP
                'penalty_weight': 1.5,
                'justification': 'External financing gaps common in crisis'
            },
            'trade_consistency': {
                'description': 'Cross-sector Trade Consistency',
                'max_relaxation': 5.0,  # Up to 5% of trade flows
                'penalty_weight': 2.0,
                'justification': 'Trade data from different sources may be inconsistent'
            },
            'statistical_discrepancy': {
                'description': 'GDP Statistical Discrepancy',
                'max_relaxation': 3.0,  # Up to 3% of GDP
                'penalty_weight': 0.5,
                'justification': 'Statistical discrepancy captures measurement errors'
            }
        }
        
        # Critical constraints that should never be relaxed
        self.critical_constraints = {
            'gdp_expenditure_nominal',
            'gdp_expenditure_real', 
            'gdp_production_nominal',
            'gdp_production_real',
            'deflator_relationships',
            'investment_decomposition',
            'consumption_decomposition'
        }
        
        logger.info(f"Initialized FeasibilityManager with {crisis_tolerance}% base tolerance")
        logger.info(f"Can relax {len(self.crisis_constraints)} crisis-specific constraints")
    
    def analyze_feasibility(self, constraint_violations: Dict[str, Dict[int, float]], 
                           years: List[int]) -> FeasibilityReport:
        """
        Analyze feasibility and determine which constraints need relaxation
        
        Args:
            constraint_violations: Current constraint violations
            years: Years being optimized
            
        Returns:
            FeasibilityReport with analysis and recommendations
        """
        logger.info("Analyzing system feasibility")
        
        infeasible_constraints = []
        total_penalty = 0.0
        slack_vars = {}
        
        # Check each constraint
        for constraint_name, year_violations in constraint_violations.items():
            slack_vars[constraint_name] = {}
            
            for year, violation in year_violations.items():
                if violation > self.crisis_tolerance:
                    # Check if this constraint can be relaxed
                    if constraint_name in self.crisis_constraints:
                        # Add slack variable for relaxation
                        max_relaxation = self.crisis_constraints[constraint_name]['max_relaxation']
                        penalty_weight = self.crisis_constraints[constraint_name]['penalty_weight']
                        
                        # Calculate required slack
                        required_slack = violation - self.crisis_tolerance
                        allowed_slack = min(required_slack, max_relaxation)
                        
                        slack_vars[constraint_name][year] = allowed_slack
                        total_penalty += penalty_weight * allowed_slack
                        
                        logger.info(f"Added slack for {constraint_name}[{year}]: {allowed_slack:.2f}%")
                        
                        if required_slack > max_relaxation:
                            infeasible_constraints.append(f"{constraint_name}[{year}]")
                    
                    elif constraint_name in self.critical_constraints:
                        # Critical constraint violated - system may be infeasible
                        infeasible_constraints.append(f"{constraint_name}[{year}]")
                        logger.warning(f"Critical constraint violated: {constraint_name}[{year}] = {violation:.2f}%")
        
        # Generate recommendations
        recommendations = self._generate_recommendations(constraint_violations, infeasible_constraints)
        
        is_feasible = len(infeasible_constraints) == 0
        
        return FeasibilityReport(
            is_feasible=is_feasible,
            infeasible_constraints=infeasible_constraints,
            slack_variables=slack_vars,
            relaxation_penalties=self.relaxation_penalties,
            total_penalty=total_penalty,
            recommendations=recommendations
        )
    
    def add_slack_variables(self, constraints: Dict[str, Any], 
                           years: List[int]) -> Dict[str, Any]:
        """
        Add L1-penalized slack variables to constraint system
        
        Args:
            constraints: Original constraint system
            years: Years being optimized
            
        Returns:
            Modified constraint system with slack variables
        """
        logger.info("Adding L1-penalized slack variables")
        
        modified_constraints = constraints.copy()
        
        # Add slack variables for crisis-specific constraints
        for constraint_name in self.crisis_constraints:
            for year in years:
                slack_var = SlackVariable(
                    constraint_name=constraint_name,
                    year=year,
                    penalty_weight=self.crisis_constraints[constraint_name]['penalty_weight'],
                    bounds=(-self.crisis_constraints[constraint_name]['max_relaxation'],
                           self.crisis_constraints[constraint_name]['max_relaxation'])
                )
                
                # Store slack variable
                if constraint_name not in self.slack_variables:
                    self.slack_variables[constraint_name] = {}
                self.slack_variables[constraint_name][year] = slack_var
        
        # Modify constraint definitions to include slack terms
        modified_constraints['slack_variables'] = self.slack_variables
        
        return modified_constraints
    
    def detect_infeasibility(self, constraint_residuals: Dict[str, Dict[int, float]]) -> List[str]:
        """
        Detect which constraints are causing infeasibility
        
        Args:
            constraint_residuals: Constraint residual values
            
        Returns:
            List of problematic constraints
        """
        problematic = []
        
        for constraint_name, year_residuals in constraint_residuals.items():
            for year, residual in year_residuals.items():
                # Check if residual is too large even with maximum relaxation
                if constraint_name in self.crisis_constraints:
                    max_allowed = (self.crisis_tolerance + 
                                 self.crisis_constraints[constraint_name]['max_relaxation'])
                    if abs(residual) > max_allowed:
                        problematic.append(f"{constraint_name}[{year}]")
                elif constraint_name in self.critical_constraints:
                    if abs(residual) > self.crisis_tolerance:
                        problematic.append(f"{constraint_name}[{year}]")
        
        return problematic
    
    def _generate_recommendations(self, constraint_violations: Dict[str, Dict[int, float]], 
                                 infeasible_constraints: List[str]) -> List[str]:
        """Generate recommendations for handling infeasibility"""
        recommendations = []
        
        if not infeasible_constraints:
            recommendations.append("✅ System is feasible with crisis economy relaxations")
            return recommendations
        
        # Analyze patterns in infeasible constraints
        critical_violations = [c for c in infeasible_constraints 
                             if any(crit in c for crit in self.critical_constraints)]
        crisis_violations = [c for c in infeasible_constraints 
                           if any(crisis in c for crisis in self.crisis_constraints)]
        
        if critical_violations:
            recommendations.append(
                f"⚠️  Critical constraints violated: {len(critical_violations)} cases"
            )
            recommendations.append(
                "Consider: 1) Adjusting target values, 2) Checking data quality, "
                "3) Using sequential approach as fallback"
            )
        
        if crisis_violations:
            recommendations.append(
                f"📊 Crisis constraints need higher relaxation: {len(crisis_violations)} cases"
            )
            recommendations.append(
                "Consider: 1) Increasing crisis tolerance, 2) Using external financing gaps"
            )
        
        # Specific recommendations based on constraint types
        violation_types = set()
        for constraint_name in constraint_violations:
            if any(year_violation > self.crisis_tolerance * 2 
                  for year_violation in constraint_violations[constraint_name].values()):
                violation_types.add(constraint_name)
        
        if 'bop_identity' in violation_types:
            recommendations.append(
                "💱 Large BOP violations suggest external financing needs or data issues"
            )
        
        if 'savings_investment' in violation_types:
            recommendations.append(
                "💰 S-I gap violations are normal for crisis economies with external financing"
            )
        
        if any('gdp' in vt for vt in violation_types):
            recommendations.append(
                "📈 GDP identity violations suggest deflator inconsistencies - check real/nominal data"
            )
        
        return recommendations
    
    def get_penalty_term(self, slack_values: Dict[str, Dict[int, float]]) -> float:
        """
        Calculate L1 penalty term for slack variables
        
        Args:
            slack_values: Current slack variable values
            
        Returns:
            Total L1 penalty
        """
        total_penalty = 0.0
        
        for constraint_name, year_values in slack_values.items():
            if constraint_name in self.crisis_constraints:
                penalty_weight = self.crisis_constraints[constraint_name]['penalty_weight']
                
                for year, slack_value in year_values.items():
                    # L1 penalty: weight * |slack_value|
                    total_penalty += penalty_weight * abs(slack_value)
        
        return total_penalty
    
    def validate_relaxations(self, proposed_relaxations: Dict[str, Dict[int, float]]) -> bool:
        """
        Validate that proposed relaxations are within acceptable bounds
        
        Args:
            proposed_relaxations: Proposed slack variable values
            
        Returns:
            True if all relaxations are acceptable
        """
        for constraint_name, year_values in proposed_relaxations.items():
            if constraint_name in self.crisis_constraints:
                max_allowed = self.crisis_constraints[constraint_name]['max_relaxation']
                
                for year, relaxation in year_values.items():
                    if abs(relaxation) > max_allowed:
                        logger.warning(
                            f"Relaxation too large for {constraint_name}[{year}]: "
                            f"{relaxation:.2f}% > {max_allowed:.2f}%"
                        )
                        return False
            elif constraint_name in self.critical_constraints:
                # Critical constraints should not be relaxed
                for year, relaxation in year_values.items():
                    if abs(relaxation) > 0.1:  # Allow tiny numerical errors
                        logger.error(
                            f"Attempted to relax critical constraint {constraint_name}[{year}]: "
                            f"{relaxation:.2f}%"
                        )
                        return False
        
        return True
    
    def get_crisis_justification(self, constraint_name: str) -> str:
        """Get justification for relaxing a crisis constraint"""
        if constraint_name in self.crisis_constraints:
            return self.crisis_constraints[constraint_name]['justification']
        return "No justification available - constraint should not be relaxed"
