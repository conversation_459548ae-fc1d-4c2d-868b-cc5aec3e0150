#!/usr/bin/env python3
"""
Solver Enhancements for Simultaneous Optimization
Implements Anderson acceleration, trust region methods, and convergence diagnostics.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
from dataclasses import dataclass, field
from collections import deque
import scipy.linalg as la

logger = logging.getLogger(__name__)


@dataclass
class ConvergenceInfo:
    """Information about optimization convergence"""
    converged: bool
    iterations: int
    final_residual: float
    residual_history: List[float] = field(default_factory=list)
    step_sizes: List[float] = field(default_factory=list)
    trust_region_radius: float = 1.0
    acceleration_applied: bool = False
    convergence_rate: Optional[float] = None


class SolverEnhancements:
    """
    Advanced numerical methods for robust optimization
    """
    
    def __init__(self, tolerance: float = 1e-6, max_iterations: int = 1000):
        """
        Initialize solver enhancements
        
        Args:
            tolerance: Convergence tolerance
            max_iterations: Maximum iterations
        """
        self.tolerance = tolerance
        self.max_iterations = max_iterations
        
        # Anderson acceleration parameters
        self.anderson_memory = 5  # m parameter
        self.anderson_beta = 1.0  # mixing parameter
        self.anderson_threshold = 1e-2  # When to start acceleration
        
        # Trust region parameters
        self.trust_radius_init = 1.0
        self.trust_radius_max = 10.0
        self.trust_radius_min = 1e-8
        self.trust_eta1 = 0.25  # Shrink threshold
        self.trust_eta2 = 0.75  # Expand threshold
        self.trust_gamma1 = 0.25  # Shrink factor
        self.trust_gamma2 = 2.0   # Expand factor
        
        logger.info("Initialized SolverEnhancements with advanced numerical methods")
    
    def anderson_accelerate(self, x_history: List[np.ndarray], 
                           f_history: List[np.ndarray], 
                           m: Optional[int] = None) -> np.ndarray:
        """
        Apply Anderson acceleration to fixed-point iterations
        
        Anderson acceleration accelerates the convergence of fixed-point iterations:
        x_{k+1} = F(x_k) by mixing previous iterates optimally.
        
        Args:
            x_history: History of x values (most recent last)
            f_history: History of F(x) values (most recent last)
            m: Memory depth (default: self.anderson_memory)
            
        Returns:
            Accelerated next iterate
        """
        if m is None:
            m = self.anderson_memory
        
        # Need at least 2 points for acceleration
        if len(x_history) < 2 or len(f_history) < 2:
            return f_history[-1] if f_history else x_history[-1]
        
        # Use last m+1 points
        m_actual = min(m, len(x_history) - 1)
        
        # Get recent history
        x_recent = x_history[-(m_actual+1):]
        f_recent = f_history[-(m_actual+1):]
        
        try:
            # Compute differences
            delta_x = np.array([x_recent[i+1] - x_recent[i] for i in range(m_actual)])
            delta_f = np.array([f_recent[i+1] - f_recent[i] for i in range(m_actual)])
            
            # Solve least squares problem: min ||delta_f @ alpha||
            if m_actual == 1:
                # Simple case
                alpha = np.array([1.0])
            else:
                # QR decomposition for numerical stability
                Q, R = la.qr(delta_f.T, mode='economic')
                if R.shape[0] > 0 and abs(R[-1, -1]) > 1e-12:
                    alpha = la.solve_triangular(R, Q.T @ delta_f[-1])
                    alpha = np.append(alpha, 1.0 - np.sum(alpha))
                else:
                    # Fallback to uniform weights
                    alpha = np.ones(m_actual + 1) / (m_actual + 1)
            
            # Normalize weights
            alpha = alpha / np.sum(alpha)
            
            # Compute accelerated iterate
            x_acc = sum(alpha[i] * f_recent[i] for i in range(len(alpha)))
            
            # Mix with standard iterate for stability
            x_standard = f_recent[-1]
            x_mixed = self.anderson_beta * x_acc + (1 - self.anderson_beta) * x_standard
            
            logger.debug(f"Anderson acceleration applied with {m_actual} memory points")
            return x_mixed
            
        except (la.LinAlgError, np.linalg.LinAlgError) as e:
            logger.warning(f"Anderson acceleration failed: {e}, using standard iterate")
            return f_history[-1]
    
    def trust_region_step(self, x: np.ndarray, grad: np.ndarray, 
                         hess: np.ndarray, delta: float) -> Tuple[np.ndarray, bool]:
        """
        Compute trust region step using dogleg method
        
        Args:
            x: Current point
            grad: Gradient at current point
            hess: Hessian at current point (or approximation)
            delta: Trust region radius
            
        Returns:
            Tuple of (step, cauchy_step_taken)
        """
        try:
            # Compute Newton step
            try:
                newton_step = -la.solve(hess, grad)
                newton_norm = la.norm(newton_step)
            except (la.LinAlgError, np.linalg.LinAlgError):
                # Hessian is singular, use steepest descent
                newton_step = None
                newton_norm = float('inf')
            
            # Compute Cauchy step (steepest descent)
            grad_norm = la.norm(grad)
            if grad_norm < 1e-12:
                return np.zeros_like(x), True
            
            # Optimal step length for quadratic model along gradient direction
            grad_hess_grad = grad.T @ hess @ grad
            if grad_hess_grad > 0:
                alpha_cauchy = (grad_norm ** 2) / grad_hess_grad
            else:
                alpha_cauchy = delta / grad_norm
            
            cauchy_step = -alpha_cauchy * grad
            cauchy_norm = la.norm(cauchy_step)
            
            # Choose step based on trust region radius
            if newton_step is not None and newton_norm <= delta:
                # Newton step is within trust region
                return newton_step, False
            elif cauchy_norm >= delta:
                # Cauchy step exceeds trust region, scale it
                return -(delta / grad_norm) * grad, True
            else:
                # Dogleg step: interpolate between Cauchy and Newton
                if newton_step is not None:
                    # Find intersection of dogleg path with trust region boundary
                    diff = newton_step - cauchy_step
                    a = la.norm(diff) ** 2
                    b = 2 * cauchy_step.T @ diff
                    c = cauchy_norm ** 2 - delta ** 2
                    
                    discriminant = b ** 2 - 4 * a * c
                    if discriminant >= 0 and a > 1e-12:
                        tau = (-b + np.sqrt(discriminant)) / (2 * a)
                        tau = max(0, min(1, tau))
                        step = cauchy_step + tau * diff
                        return step, False
                
                # Fallback to scaled Cauchy step
                return cauchy_step, True
                
        except Exception as e:
            logger.warning(f"Trust region step computation failed: {e}")
            # Fallback to scaled gradient step
            grad_norm = la.norm(grad)
            if grad_norm > 1e-12:
                return -(delta / grad_norm) * grad, True
            else:
                return np.zeros_like(x), True
    
    def update_trust_radius(self, delta: float, actual_reduction: float, 
                           predicted_reduction: float) -> float:
        """
        Update trust region radius based on step quality
        
        Args:
            delta: Current trust region radius
            actual_reduction: Actual function reduction
            predicted_reduction: Predicted reduction from model
            
        Returns:
            Updated trust region radius
        """
        if abs(predicted_reduction) < 1e-12:
            # No predicted reduction, keep radius
            return delta
        
        # Compute ratio of actual to predicted reduction
        rho = actual_reduction / predicted_reduction
        
        if rho < self.trust_eta1:
            # Poor step, shrink radius
            new_delta = self.trust_gamma1 * delta
        elif rho > self.trust_eta2:
            # Good step, expand radius
            new_delta = min(self.trust_gamma2 * delta, self.trust_radius_max)
        else:
            # Acceptable step, keep radius
            new_delta = delta
        
        # Ensure radius stays within bounds
        new_delta = max(new_delta, self.trust_radius_min)
        
        logger.debug(f"Trust radius: {delta:.2e} -> {new_delta:.2e} (rho={rho:.3f})")
        return new_delta
    
    def diagnose_convergence(self, residual_history: List[float], 
                           step_history: List[np.ndarray]) -> ConvergenceInfo:
        """
        Diagnose convergence behavior and provide recommendations
        
        Args:
            residual_history: History of residual norms
            step_history: History of step vectors
            
        Returns:
            ConvergenceInfo with detailed analysis
        """
        if not residual_history:
            return ConvergenceInfo(
                converged=False,
                iterations=0,
                final_residual=float('inf')
            )
        
        final_residual = residual_history[-1]
        converged = final_residual < self.tolerance
        iterations = len(residual_history)
        
        # Compute step sizes
        step_sizes = [la.norm(step) for step in step_history] if step_history else []
        
        # Estimate convergence rate
        convergence_rate = None
        if len(residual_history) >= 3:
            # Linear convergence rate: r_{k+1} / r_k
            rates = []
            for i in range(1, min(10, len(residual_history))):
                if residual_history[-i-1] > 1e-12:
                    rate = residual_history[-i] / residual_history[-i-1]
                    rates.append(rate)
            
            if rates:
                convergence_rate = np.mean(rates)
        
        info = ConvergenceInfo(
            converged=converged,
            iterations=iterations,
            final_residual=final_residual,
            residual_history=residual_history.copy(),
            step_sizes=step_sizes,
            convergence_rate=convergence_rate
        )
        
        # Log convergence analysis
        if converged:
            logger.info(f"✅ Converged in {iterations} iterations (residual: {final_residual:.2e})")
        else:
            logger.warning(f"❌ Not converged after {iterations} iterations (residual: {final_residual:.2e})")
        
        if convergence_rate is not None:
            if convergence_rate < 0.1:
                logger.info(f"🚀 Fast convergence (rate: {convergence_rate:.3f})")
            elif convergence_rate < 0.9:
                logger.info(f"📈 Good convergence (rate: {convergence_rate:.3f})")
            else:
                logger.warning(f"🐌 Slow convergence (rate: {convergence_rate:.3f})")
        
        return info
    
    def adaptive_step_size(self, iteration: int, residual: float, 
                          prev_residual: Optional[float] = None) -> float:
        """
        Compute adaptive step size based on convergence behavior
        
        Args:
            iteration: Current iteration number
            residual: Current residual norm
            prev_residual: Previous residual norm
            
        Returns:
            Recommended step size
        """
        base_step = 1.0
        
        # Reduce step size in early iterations
        if iteration < 10:
            base_step *= 0.5
        
        # Adjust based on residual behavior
        if prev_residual is not None and prev_residual > 1e-12:
            reduction_ratio = residual / prev_residual
            
            if reduction_ratio > 1.1:
                # Residual increasing, reduce step
                base_step *= 0.5
            elif reduction_ratio < 0.5:
                # Good reduction, can increase step
                base_step *= min(1.5, 1.0 / reduction_ratio)
        
        # Bound step size
        return max(0.01, min(2.0, base_step))
    
    def check_stagnation(self, residual_history: List[float], 
                        window: int = 10) -> bool:
        """
        Check if optimization has stagnated
        
        Args:
            residual_history: History of residual values
            window: Window size for stagnation check
            
        Returns:
            True if stagnation detected
        """
        if len(residual_history) < window:
            return False
        
        recent_residuals = residual_history[-window:]
        
        # Check if residuals are not decreasing significantly
        max_recent = max(recent_residuals)
        min_recent = min(recent_residuals)
        
        if max_recent < 1e-12:
            return False  # Already converged
        
        relative_improvement = (max_recent - min_recent) / max_recent
        
        stagnated = relative_improvement < 0.01  # Less than 1% improvement
        
        if stagnated:
            logger.warning(f"Stagnation detected: {relative_improvement:.1%} improvement in {window} iterations")
        
        return stagnated
