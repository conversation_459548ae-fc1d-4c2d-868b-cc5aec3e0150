#!/usr/bin/env python3
"""
Block Coordinate <PERSON>cent <PERSON>ver for Simultaneous Optimization
Implements block coordinate descent with Augmented Lagrangian method for deflator optimization.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
from dataclasses import dataclass
import scipy.optimize as opt

try:
    from .data_handler import YemenDataHandler
    from .target_processor import IMFTargetProcessor
    from .sparse_constraints import SparseConstraintSystem
except ImportError:
    from data_handler import YemenDataHandler
    from target_processor import IMFTargetProcessor
    from sparse_constraints import SparseConstraintSystem

logger = logging.getLogger(__name__)


@dataclass
class BlockSolution:
    """Solution for a single block optimization"""
    success: bool
    deflator_values: Dict[str, float]
    objective_value: float
    constraint_violations: Dict[str, float]
    iterations: int


class BlockCoordinateDescent:
    """
    Implements block coordinate descent solver with economic structure exploitation
    """
    
    def __init__(self, data_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, target_processor: IMFTargetProcessor):
        """
        Initialize block coordinate descent solver
        
        Args:
            data_handler: Handler for Yemen macro data
            target_processor: Processor for IMF targets
        """
        self.data = data_handler
        self.targets = target_processor
        
        # Define block structure based on economic relationships
        self.blocks = self._define_block_structure()
        
        # Optimization parameters
        self.max_block_iterations = 100
        self.block_tolerance = 1e-4
        self.penalty_param = 10.0

        # Constraint weighting - much higher priority for crisis economy
        self.constraint_weight = 1000.0  # High priority for constraint satisfaction

        # Block coordination parameters
        self.block_consistency_weight = 500.0  # Weight for inter-block consistency
        self.violation_threshold = 0.1  # Threshold for prioritizing blocks

        # Trust region parameters for block optimization
        self.trust_radius_init = 1.0
        self.trust_radius_max = 10.0
        self.trust_radius_min = 1e-6
        self.trust_eta1 = 0.1   # Threshold for successful step
        self.trust_eta2 = 0.75  # Threshold for very successful step
        self.trust_gamma1 = 0.25  # Shrink factor
        self.trust_gamma2 = 2.0   # Expand factor
        
        logger.info(f"Initialized BlockCoordinateDescent with {len(self.blocks)} blocks")
    
    def _define_block_structure(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Define block structure for coordinate descent
        
        Returns:
            Dictionary defining blocks and their deflator variables
        """
        blocks = {
            'gdp_block': {
                'description': 'Variables affecting GDP identities',
                'deflators': [
                    'gdp_deflator',
                    'c_private_deflator',
                    'c_government_deflator',
                    'i_total_deflator',
                    'inventory_deflator',
                    'stat_disc_deflator'
                ],
                'primary_targets': ['gdp_usd'],
                'constraints': ['gdp_expenditure_nominal', 'gdp_expenditure_real']
            },
            
            'fiscal_block': {
                'description': 'Government revenue and expenditure deflators',
                'deflators': [
                    'c_government_deflator',
                    'i_government_deflator',
                    'net_taxes_deflator'
                ],
                'primary_targets': ['fiscal_balance', 'government_expenditure'],
                'constraints': ['fiscal_identities', 'gdp_fiscal_consistency']
            },
            
            'trade_block': {
                'description': 'Import and export deflators',
                'deflators': [
                    'exports_deflator',
                    'imports_deflator'
                ],
                'primary_targets': ['imports_usd', 'exports_usd'],
                'constraints': ['external_sector', 'trade_consistency']
            },
            
            'production_block': {
                'description': 'Production sector deflators',
                'deflators': [
                    'agriculture_deflator',
                    'industry_deflator',
                    'services_deflator'
                ],
                'primary_targets': [],  # Derived from GDP targets
                'constraints': ['gdp_production_nominal', 'gdp_production_real']
            },
            
            'investment_block': {
                'description': 'Investment decomposition deflators',
                'deflators': [
                    'i_private_deflator',
                    'i_government_deflator'
                ],
                'primary_targets': [],  # Derived from total investment
                'constraints': ['investment_decomposition']
            }
        }
        
        return blocks
    
    def solve_blocks(self, initial_deflators: Dict[str, Dict[int, float]], 
                    years: List[int], 
                    lagrange_multipliers: Dict[str, Dict[str, Dict[int, float]]]) -> Dict[str, BlockSolution]:
        """
        Solve all blocks using coordinate descent
        
        Args:
            initial_deflators: Initial deflator values
            years: Years to optimize
            lagrange_multipliers: Current Lagrange multipliers
            
        Returns:
            Dictionary of block solutions
        """
        logger.info(f"Solving {len(self.blocks)} blocks for years {years}")
        
        block_solutions = {}
        current_deflators = initial_deflators.copy()
        
        # Iterate through blocks
        for iteration in range(self.max_block_iterations):
            max_change = 0.0
            iteration_solutions = {}

            # Get current constraint violations for block scheduling
            try:
                violations = {}
                if hasattr(self, 'constraint_system') and self.constraint_system:
                    violations = self.constraint_system.evaluate_constraints(current_deflators, years)
            except:
                violations = {}

            # Get block schedule based on violation severity
            block_schedule = self._get_block_schedule(violations)

            # Solve blocks in priority order
            for block_name in block_schedule:
                if block_name not in self.blocks:
                    continue

                block_info = self.blocks[block_name]
                logger.debug(f"Solving {block_name} (iteration {iteration + 1}, priority order)")

                # Solve this block while keeping others fixed
                solution = self._solve_single_block(
                    block_name, block_info, current_deflators, years, lagrange_multipliers
                )
                
                iteration_solutions[block_name] = solution

                if solution.success:
                    # Update deflators for this block
                    for deflator_name in block_info['deflators']:
                        if deflator_name in solution.deflator_values:
                            for year in years:
                                old_value = current_deflators.get(deflator_name, {}).get(year, 100.0)
                                new_value = solution.deflator_values[deflator_name]

                                if deflator_name not in current_deflators:
                                    current_deflators[deflator_name] = {}
                                current_deflators[deflator_name][year] = new_value

                                # Track maximum change for convergence
                                change = abs(new_value - old_value) / max(abs(old_value), 1.0)
                                max_change = max(max_change, change)

            # Enforce consistency across blocks after all are solved
            if iteration_solutions:
                consistent_deflators = self._enforce_block_consistency(iteration_solutions, years)

                # Apply consistent deflator values
                for deflator_name, year_values in consistent_deflators.items():
                    if deflator_name not in current_deflators:
                        current_deflators[deflator_name] = {}
                    for year, value in year_values.items():
                        old_value = current_deflators[deflator_name].get(year, 100.0)
                        current_deflators[deflator_name][year] = value

                        # Update max change tracking
                        change = abs(value - old_value) / max(abs(old_value), 1.0)
                        max_change = max(max_change, change)

                # Store solutions for this iteration
                block_solutions.update(iteration_solutions)
            
            # Check convergence
            if max_change < self.block_tolerance:
                logger.info(f"Block coordinate descent converged after {iteration + 1} iterations")
                break
        
        return block_solutions
    
    def _solve_single_block(self, block_name: str, block_info: Dict[str, Any],
                           current_deflators: Dict[str, Dict[int, float]], 
                           years: List[int],
                           lagrange_multipliers: Dict[str, Dict[str, Dict[int, float]]]) -> BlockSolution:
        """
        Solve optimization for a single block
        
        Args:
            block_name: Name of the block
            block_info: Block configuration
            current_deflators: Current deflator values
            years: Years to optimize
            lagrange_multipliers: Lagrange multipliers
            
        Returns:
            BlockSolution for this block
        """
        try:
            if block_name == 'gdp_block':
                return self._solve_gdp_block(block_info, current_deflators, years, lagrange_multipliers)
            elif block_name == 'fiscal_block':
                return self._solve_fiscal_block(block_info, current_deflators, years, lagrange_multipliers)
            elif block_name == 'trade_block':
                return self._solve_trade_block(block_info, current_deflators, years, lagrange_multipliers)
            elif block_name == 'production_block':
                return self._solve_production_block(block_info, current_deflators, years, lagrange_multipliers)
            elif block_name == 'investment_block':
                return self._solve_investment_block(block_info, current_deflators, years, lagrange_multipliers)
            else:
                logger.warning(f"Unknown block type: {block_name}")
                return BlockSolution(
                    success=False,
                    deflator_values={},
                    objective_value=float('inf'),
                    constraint_violations={},
                    iterations=0
                )
                
        except Exception as e:
            logger.error(f"Error solving {block_name}: {str(e)}")
            return BlockSolution(
                success=False,
                deflator_values={},
                objective_value=float('inf'),
                constraint_violations={},
                iterations=0
            )
    
    def _solve_gdp_block(self, block_info: Dict[str, Any],
                        current_deflators: Dict[str, Dict[int, float]],
                        years: List[int],
                        lagrange_multipliers: Dict) -> BlockSolution:
        """Solve GDP block with proper optimization"""
        logger.debug("Solving GDP block")

        # Get deflators for this block
        block_deflators = block_info['deflators']

        # Create sub-problem for this block
        def block_objective(x_block):
            # Create deflator dictionary for this block
            deflators = {}
            for i, deflator_name in enumerate(block_deflators):
                deflators[deflator_name] = {}
                for year in years:
                    deflators[deflator_name][year] = x_block[i] if i < len(x_block) else 100.0

            # Target deviations (only GDP-related)
            gdp_dev = self._calculate_gdp_deviation(deflators, years)

            # Constraint violations for GDP identities
            violation_sum = 0.0

            # Simple constraint evaluation for GDP expenditure
            for year in years:
                # Apply deflators temporarily to calculate constraint violations
                original_data = self.data.df.copy()
                try:
                    # Apply GDP deflator if in block
                    if 'gdp_deflator' in deflators:
                        gdp_real = self.data.get_variable('YEMNYGDPMKTPKN', [year]).get(year, 0)
                        if gdp_real > 0:
                            new_gdp_nominal = gdp_real * deflators['gdp_deflator'][year] / 100
                            self.data.update_variable('YEMNYGDPMKTPCN', year, new_gdp_nominal)

                    # Calculate GDP expenditure constraint violation
                    gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 0)
                    c_private = self.data.get_variable('YEMNECONPRVTCN', [year]).get(year, 0)
                    c_govt = self.data.get_variable('YEMNECONGOVTCN', [year]).get(year, 0)
                    investment = self.data.get_variable('YEMNEGDIFTOTCN', [year]).get(year, 0)
                    exports = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
                    imports = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

                    # GDP = C + I + G + (X - M)
                    calculated_gdp = c_private + c_govt + investment + exports - imports
                    if gdp_nominal > 0:
                        violation = abs(gdp_nominal - calculated_gdp) / gdp_nominal * 100
                        violation_sum += self.constraint_weight * (self.penalty_param / 2) * violation**2

                finally:
                    self.data.df = original_data

            return gdp_dev + violation_sum

        # Get current block values (use first deflator as representative)
        x_block_init = []
        for deflator_name in block_deflators:
            if deflator_name in current_deflators and years[0] in current_deflators[deflator_name]:
                x_block_init.append(current_deflators[deflator_name][years[0]])
            else:
                x_block_init.append(100.0)

        x_block_init = np.array(x_block_init)

        # Solve sub-problem
        result = opt.minimize(
            block_objective,
            x_block_init,
            method='L-BFGS-B',
            bounds=[(20.0, 200.0)] * len(x_block_init)
        )

        # Update deflator values
        updated_deflators = {}
        for i, deflator_name in enumerate(block_deflators):
            if i < len(result.x):
                updated_deflators[deflator_name] = result.x[i]

        return BlockSolution(
            success=result.success,
            deflator_values=updated_deflators,
            objective_value=result.fun,
            constraint_violations={},
            iterations=result.nit if hasattr(result, 'nit') else 1
        )
    
    def _solve_fiscal_block(self, block_info: Dict[str, Any],
                           current_deflators: Dict[str, Dict[int, float]],
                           years: List[int],
                           lagrange_multipliers: Dict) -> BlockSolution:
        """Solve fiscal block optimization"""
        logger.debug("Solving fiscal block")

        # Get deflators for this block
        block_deflators = block_info['deflators']

        # Create sub-problem for this block
        def block_objective(x_block):
            # Create deflator dictionary for this block
            deflators = {}
            for i, deflator_name in enumerate(block_deflators):
                deflators[deflator_name] = {}
                for year in years:
                    deflators[deflator_name][year] = x_block[i] if i < len(x_block) else 100.0

            # Target deviations (fiscal-related)
            fiscal_dev = self._calculate_fiscal_deviation(deflators, years)

            # Add constraint violations for fiscal-related identities
            constraint_penalty = self._calculate_block_constraint_penalty(deflators, years, ['gdp_expenditure_nominal'])

            return fiscal_dev + constraint_penalty

        # Initial values for this block's deflators
        x0 = []
        bounds = []
        for deflator_name in block_deflators:
            current_value = current_deflators.get(deflator_name, {}).get(years[0], 100.0)
            x0.append(current_value)
            bounds.append((50.0, 200.0))  # Reasonable bounds for deflators

        # Optimize using trust region method for better robustness
        try:
            result = self._optimize_with_trust_region(block_objective, np.array(x0), bounds)
        except Exception as e:
            logger.warning(f"Trust region optimization failed: {e}, falling back to L-BFGS-B")
            result = opt.minimize(
                block_objective,
                x0,
                method='L-BFGS-B',
                bounds=bounds
            )

        # Update deflator values
        updated_deflators = {}
        for i, deflator_name in enumerate(block_deflators):
            updated_deflators[deflator_name] = result.x[i]

        return BlockSolution(
            success=result.success,
            deflator_values=updated_deflators,
            objective_value=result.fun,
            constraint_violations={},
            iterations=result.nit if hasattr(result, 'nit') else 1
        )
    
    def _solve_trade_block(self, block_info: Dict[str, Any],
                          current_deflators: Dict[str, Dict[int, float]],
                          years: List[int],
                          lagrange_multipliers: Dict) -> BlockSolution:
        """Solve trade block with proper optimization"""
        logger.debug("Solving trade block")

        # Get deflators for this block
        block_deflators = block_info['deflators']

        # Create sub-problem for this block
        def block_objective(x_block):
            # Create deflator dictionary for this block
            deflators = {}
            for i, deflator_name in enumerate(block_deflators):
                deflators[deflator_name] = {}
                for year in years:
                    deflators[deflator_name][year] = x_block[i] if i < len(x_block) else 100.0

            # Target deviations (trade-related)
            trade_dev = self._calculate_trade_deviation(deflators, years)

            # Add constraint violations for trade-related identities
            constraint_penalty = self._calculate_block_constraint_penalty(deflators, years, ['bop_identity'])

            return trade_dev + constraint_penalty

        # Get current block values
        x_block_init = []
        for deflator_name in block_deflators:
            if deflator_name in current_deflators and years[0] in current_deflators[deflator_name]:
                x_block_init.append(current_deflators[deflator_name][years[0]])
            else:
                x_block_init.append(100.0)

        x_block_init = np.array(x_block_init)

        # Solve sub-problem
        result = opt.minimize(
            block_objective,
            x_block_init,
            method='L-BFGS-B',
            bounds=[(20.0, 200.0)] * len(x_block_init)
        )

        # Update deflator values
        updated_deflators = {}
        for i, deflator_name in enumerate(block_deflators):
            if i < len(result.x):
                updated_deflators[deflator_name] = result.x[i]

        return BlockSolution(
            success=result.success,
            deflator_values=updated_deflators,
            objective_value=result.fun,
            constraint_violations={},
            iterations=result.nit if hasattr(result, 'nit') else 1
        )
    
    def _solve_production_block(self, block_info: Dict[str, Any],
                               current_deflators: Dict[str, Dict[int, float]], 
                               years: List[int],
                               lagrange_multipliers: Dict) -> BlockSolution:
        """Solve production block optimization"""
        logger.debug("Solving production block")
        
        # Placeholder - production deflators are typically derived from GDP targets
        return BlockSolution(
            success=True,
            deflator_values={},
            objective_value=0.0,
            constraint_violations={},
            iterations=1
        )
    
    def _solve_investment_block(self, block_info: Dict[str, Any],
                               current_deflators: Dict[str, Dict[int, float]],
                               years: List[int],
                               lagrange_multipliers: Dict) -> BlockSolution:
        """Solve investment block optimization"""
        logger.debug("Solving investment block")

        # Get deflators for this block
        block_deflators = block_info['deflators']

        # Create sub-problem for this block
        def block_objective(x_block):
            # Create deflator dictionary for this block
            deflators = {}
            for i, deflator_name in enumerate(block_deflators):
                deflators[deflator_name] = {}
                for year in years:
                    deflators[deflator_name][year] = x_block[i] if i < len(x_block) else 100.0

            # Target deviations (investment-related)
            investment_dev = self._calculate_investment_deviation(deflators, years)

            # Add constraint violations for investment-related identities
            constraint_penalty = self._calculate_block_constraint_penalty(deflators, years, ['investment_decomposition', 'savings_investment'])

            return investment_dev + constraint_penalty

        # Initial values for this block's deflators
        x0 = []
        bounds = []
        for deflator_name in block_deflators:
            current_value = current_deflators.get(deflator_name, {}).get(years[0], 100.0)
            x0.append(current_value)
            bounds.append((50.0, 200.0))  # Reasonable bounds for deflators

        # Optimize
        result = opt.minimize(
            block_objective,
            x0,
            method='L-BFGS-B',
            bounds=bounds
        )

        # Update deflator values
        updated_deflators = {}
        for i, deflator_name in enumerate(block_deflators):
            updated_deflators[deflator_name] = result.x[i]

        return BlockSolution(
            success=result.success,
            deflator_values=updated_deflators,
            objective_value=result.fun,
            constraint_violations={},
            iterations=result.nit if hasattr(result, 'nit') else 1
        )
    
    def update_multipliers(self, constraint_violations: Dict[str, Dict[int, float]],
                          lagrange_multipliers: Dict[str, Dict[str, Dict[int, float]]],
                          penalty_param: float) -> Dict[str, Dict[str, Dict[int, float]]]:
        """
        Update Lagrange multipliers based on constraint violations
        
        Args:
            constraint_violations: Current constraint violations
            lagrange_multipliers: Current multipliers
            penalty_param: Penalty parameter
            
        Returns:
            Updated Lagrange multipliers
        """
        updated_multipliers = lagrange_multipliers.copy()
        
        # Update multipliers using standard Augmented Lagrangian update rule:
        # λ_new = λ_old + ρ * constraint_violation
        
        for constraint_type in updated_multipliers:
            for constraint_name in updated_multipliers[constraint_type]:
                if constraint_name in constraint_violations:
                    for year in updated_multipliers[constraint_type][constraint_name]:
                        if year in constraint_violations[constraint_name]:
                            violation = constraint_violations[constraint_name][year]
                            old_multiplier = updated_multipliers[constraint_type][constraint_name][year]
                            new_multiplier = old_multiplier + penalty_param * violation
                            updated_multipliers[constraint_type][constraint_name][year] = new_multiplier
        
        return updated_multipliers

    def _calculate_block_constraint_penalty(self, deflators: Dict[str, Dict[int, float]],
                                          years: List[int],
                                          constraint_names: List[str]) -> float:
        """Calculate constraint penalty for specific constraints relevant to this block"""
        # For now, use a simplified constraint evaluation
        # This will be enhanced when constraint system is properly integrated
        penalty = 0.0

        try:
            # Simple constraint evaluation for key identities
            for year in years:
                if 'bop_identity' in constraint_names:
                    # Balance of payments constraint
                    exports = self.data.get_variable('YEMNXGSRVN', [year]).get(year, 0)
                    imports = self.data.get_variable('YEMNMGSRVN', [year]).get(year, 0)
                    current_account = self.data.get_variable('YEMNBCAGDPN', [year]).get(year, 0)

                    if exports > 0 and imports > 0:
                        # Simplified BOP identity: CA = X - M (approximately)
                        calculated_ca = exports - imports
                        if current_account != 0:
                            violation = abs(current_account - calculated_ca) / abs(current_account) * 100
                            penalty += self.constraint_weight * (self.penalty_param / 2) * violation**2

                if 'savings_investment' in constraint_names:
                    # Savings-Investment gap constraint
                    savings = self.data.get_variable('YEMNSGDPN', [year]).get(year, 0)
                    investment = self.data.get_variable('YEMNIGDPN', [year]).get(year, 0)

                    if savings > 0 and investment > 0:
                        # S-I gap should be consistent with current account
                        si_gap = savings - investment
                        violation = abs(si_gap) / max(abs(savings), abs(investment)) * 100
                        penalty += self.constraint_weight * (self.penalty_param / 2) * violation**2

            return penalty

        except Exception as e:
            logger.warning(f"Error calculating block constraint penalty: {e}")
            return 0.0

    def _calculate_gdp_deviation(self, deflators: Dict[str, Dict[int, float]], years: List[int]) -> float:
        """Calculate GDP target deviation for block optimization"""
        total_deviation = 0.0

        # Get GDP targets
        gdp_targets = self.targets.get_gdp_targets(years)

        for year in years:
            if year in gdp_targets:
                target = gdp_targets[year]
                exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)

                # Apply GDP deflator if available
                if 'gdp_deflator' in deflators and year in deflators['gdp_deflator']:
                    gdp_real = self.data.get_variable('YEMNYGDPMKTPKN', [year]).get(year, 0)
                    gdp_nominal = gdp_real * deflators['gdp_deflator'][year] / 100
                else:
                    gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 0)

                if exchange_rate > 0 and target > 0:
                    achieved_usd = gdp_nominal / (exchange_rate * 1000)
                    deviation = ((achieved_usd - target) / target) ** 2
                    total_deviation += 10.0 * deviation  # Weight = 10

        return total_deviation

    def _calculate_trade_deviation(self, deflators: Dict[str, Dict[int, float]], years: List[int]) -> float:
        """Calculate trade target deviation for block optimization"""
        total_deviation = 0.0

        # Get trade targets
        trade_targets = self.targets.get_trade_targets(years)

        for year in years:
            exchange_rate = self.data.get_variable('YEMPANUSATLS', [year]).get(year, 1)

            # Imports target
            if 'imports' in trade_targets and year in trade_targets['imports']:
                target = trade_targets['imports'][year]

                if 'imports_deflator' in deflators and year in deflators['imports_deflator']:
                    imports_real = self.data.get_variable('YEMNEIMPGNFSKN', [year]).get(year, 0)
                    imports_nominal = imports_real * deflators['imports_deflator'][year] / 100
                else:
                    imports_nominal = self.data.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)

                if exchange_rate > 0 and target > 0:
                    achieved_usd = imports_nominal / (exchange_rate * 1000)
                    deviation = ((achieved_usd - target) / target) ** 2
                    total_deviation += 5.0 * deviation  # Weight = 5

            # Exports target
            if 'exports' in trade_targets and year in trade_targets['exports']:
                target = trade_targets['exports'][year]

                if 'exports_deflator' in deflators and year in deflators['exports_deflator']:
                    exports_real = self.data.get_variable('YEMNEEXPGNFSKN', [year]).get(year, 0)
                    exports_nominal = exports_real * deflators['exports_deflator'][year] / 100
                else:
                    exports_nominal = self.data.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)

                if exchange_rate > 0 and target > 0:
                    achieved_usd = exports_nominal / (exchange_rate * 1000)
                    deviation = ((achieved_usd - target) / target) ** 2
                    total_deviation += 5.0 * deviation  # Weight = 5

        return total_deviation

    def _calculate_fiscal_deviation(self, deflators: Dict[str, Dict[int, float]], years: List[int]) -> float:
        """Calculate fiscal target deviations"""
        total_dev = 0.0

        for year in years:
            # Government expenditure target
            govt_exp_target = self.targets.get_target('government_expenditure', year)
            if govt_exp_target is not None:
                govt_exp_actual = self.data.get_variable('YEMNGGDPN', [year]).get(year, 0)
                if govt_exp_actual > 0:
                    deviation = abs(govt_exp_actual - govt_exp_target) / govt_exp_target * 100
                    total_dev += 3.0 * deviation**2  # Weight: 3 for fiscal targets

        return total_dev

    def _calculate_investment_deviation(self, deflators: Dict[str, Dict[int, float]], years: List[int]) -> float:
        """Calculate investment target deviations"""
        total_dev = 0.0

        for year in years:
            # Investment as % of GDP target
            investment_target = self.targets.get_target('investment_percent_gdp', year)
            if investment_target is not None:
                investment_actual = self.data.get_variable('YEMNIGDPN', [year]).get(year, 0)
                gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 1)
                if gdp_nominal > 0:
                    investment_percent = (investment_actual / gdp_nominal) * 100
                    deviation = abs(investment_percent - investment_target) / investment_target * 100
                    total_dev += 2.0 * deviation**2  # Weight: 2 for investment targets

        return total_dev

    def _get_block_schedule(self, constraint_violations: Dict[str, Dict[int, float]]) -> List[str]:
        """Prioritize blocks based on violation severity"""
        block_priorities = {}

        # Calculate priority score for each block based on related constraint violations
        for block_name, block_info in self.blocks.items():
            priority_score = 0.0

            # Check violations for constraints related to this block
            if 'constraints' in block_info:
                for constraint_name in block_info['constraints']:
                    if constraint_name in constraint_violations:
                        for year, violation in constraint_violations[constraint_name].items():
                            priority_score += abs(violation)

            # GDP block gets highest priority due to its central role
            if block_name == 'gdp_block':
                priority_score *= 2.0

            block_priorities[block_name] = priority_score

        # Sort blocks by priority (highest first)
        sorted_blocks = sorted(block_priorities.items(), key=lambda x: x[1], reverse=True)

        # Return ordered list of block names
        scheduled_blocks = [block_name for block_name, _ in sorted_blocks]

        logger.debug(f"Block schedule: {scheduled_blocks}")
        return scheduled_blocks

    def _enforce_block_consistency(self, block_solutions: Dict[str, 'BlockSolution'], years: List[int]) -> Dict[str, Dict[int, float]]:
        """Ensure blocks don't conflict on shared variables"""
        consistent_deflators = {}

        # Collect all deflator values from blocks
        deflator_values = {}  # deflator_name -> list of (block_name, value) pairs

        for block_name, solution in block_solutions.items():
            if solution.success:
                for deflator_name, value in solution.deflator_values.items():
                    if deflator_name not in deflator_values:
                        deflator_values[deflator_name] = []
                    deflator_values[deflator_name].append((block_name, value))

        # Resolve conflicts using weighted averaging
        for deflator_name, value_pairs in deflator_values.items():
            if len(value_pairs) == 1:
                # No conflict
                consistent_deflators[deflator_name] = {}
                for year in years:
                    consistent_deflators[deflator_name][year] = value_pairs[0][1]
            else:
                # Multiple blocks suggest different values - use weighted average
                weights = {
                    'gdp_block': 3.0,      # Highest weight
                    'trade_block': 2.0,    # Medium weight
                    'fiscal_block': 1.5,   # Medium-low weight
                    'production_block': 1.0,  # Low weight
                    'investment_block': 1.0   # Low weight
                }

                total_weight = 0.0
                weighted_sum = 0.0

                for block_name, value in value_pairs:
                    weight = weights.get(block_name, 1.0)
                    weighted_sum += weight * value
                    total_weight += weight

                if total_weight > 0:
                    consistent_value = weighted_sum / total_weight
                    consistent_deflators[deflator_name] = {}
                    for year in years:
                        consistent_deflators[deflator_name][year] = consistent_value

                    logger.debug(f"Resolved conflict for {deflator_name}: {consistent_value:.2f}")

        return consistent_deflators

    def _optimize_with_trust_region(self, objective_func: Callable, x0: np.ndarray, bounds: List[Tuple[float, float]]) -> 'opt.OptimizeResult':
        """Optimize using trust region method with adaptive radius"""

        # Initialize trust region
        trust_radius = self.trust_radius_init
        x_current = x0.copy()

        # Try to get solver enhancements for trust region computation
        try:
            from .solver_enhancements import SolverEnhancements
            solver_enhancements = SolverEnhancements()
        except:
            # Fallback to scipy optimization
            return opt.minimize(objective_func, x0, method='L-BFGS-B', bounds=bounds)

        max_trust_iterations = 50
        tolerance = 1e-6

        for iteration in range(max_trust_iterations):
            # Evaluate function and gradient at current point
            f_current = objective_func(x_current)

            # Compute numerical gradient
            grad = self._compute_numerical_gradient(objective_func, x_current)

            # Compute approximate Hessian (BFGS or finite differences)
            hess = self._compute_approximate_hessian(objective_func, x_current, grad)

            # Compute trust region step
            try:
                step, is_cauchy = solver_enhancements.trust_region_step(x_current, grad, hess, trust_radius)
            except:
                # Fallback to gradient step
                grad_norm = np.linalg.norm(grad)
                if grad_norm > 1e-12:
                    step = -(trust_radius / grad_norm) * grad
                else:
                    break

            # Ensure step respects bounds
            x_new = x_current + step
            x_new = np.clip(x_new, [b[0] for b in bounds], [b[1] for b in bounds])
            step = x_new - x_current

            # Evaluate function at new point
            f_new = objective_func(x_new)

            # Compute actual vs predicted reduction
            predicted_reduction = -grad.T @ step - 0.5 * step.T @ hess @ step
            actual_reduction = f_current - f_new

            # Compute ratio
            if abs(predicted_reduction) > 1e-12:
                rho = actual_reduction / predicted_reduction
            else:
                rho = 0.0

            # Update trust region radius and accept/reject step
            if rho < self.trust_eta1:
                # Poor step, shrink trust region
                trust_radius = max(self.trust_gamma1 * trust_radius, self.trust_radius_min)
            else:
                # Accept step
                x_current = x_new

                if rho > self.trust_eta2:
                    # Very good step, expand trust region
                    trust_radius = min(self.trust_gamma2 * trust_radius, self.trust_radius_max)

            # Check convergence
            if np.linalg.norm(grad) < tolerance or np.linalg.norm(step) < tolerance:
                break

        # Create result object
        result = type('OptimizeResult', (), {})()
        result.x = x_current
        result.fun = objective_func(x_current)
        result.success = True
        result.nit = iteration + 1

        return result

    def _compute_numerical_gradient(self, func: Callable, x: np.ndarray, eps: float = 1e-8) -> np.ndarray:
        """Compute numerical gradient using finite differences"""
        grad = np.zeros_like(x)
        f_x = func(x)

        for i in range(len(x)):
            x_plus = x.copy()
            x_plus[i] += eps
            f_plus = func(x_plus)
            grad[i] = (f_plus - f_x) / eps

        return grad

    def _compute_approximate_hessian(self, func: Callable, x: np.ndarray, grad: np.ndarray, eps: float = 1e-6) -> np.ndarray:
        """Compute approximate Hessian using finite differences"""
        n = len(x)
        hess = np.eye(n)  # Start with identity as approximation

        # For efficiency, use a simple diagonal approximation
        for i in range(n):
            x_plus = x.copy()
            x_plus[i] += eps
            grad_plus = self._compute_numerical_gradient(func, x_plus, eps)

            # Diagonal element
            if abs(eps) > 1e-12:
                hess[i, i] = (grad_plus[i] - grad[i]) / eps

            # Ensure positive definiteness
            if hess[i, i] <= 0:
                hess[i, i] = 1.0

        return hess
